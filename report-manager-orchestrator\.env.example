# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.1

# API Server Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=orchestrator.log

# System Configuration
MAX_AGENTS_PER_FLOW=3
DEFAULT_TIMEOUT=30
ENABLE_CACHING=true

# Database Configuration - Sales Database (PostgreSQL)
SALES_DB_HOST=localhost
SALES_DB_PORT=5432
SALES_DB_NAME=sales_db
SALES_DB_USER=readonly_user
SALES_DB_PASSWORD=your_sales_db_password

# Database Configuration - HR Database (MySQL)
HR_DB_HOST=localhost
HR_DB_PORT=3306
HR_DB_NAME=hr_db
HR_DB_USER=hr_readonly
HR_DB_PASSWORD=your_hr_db_password

# Database Configuration - Analytics Database (SQLite)
ANALYTICS_DB_PATH=./data/analytics.db

# Security Configuration
DB_ENCRYPTION_KEY=your_32_character_encryption_key_here
SESSION_SECRET_KEY=your_session_secret_key_here

# Performance Configuration
QUERY_TIMEOUT=30
MAX_CONNECTIONS_PER_POOL=10
ENABLE_QUERY_CACHING=true
CACHE_TTL_SECONDS=300
