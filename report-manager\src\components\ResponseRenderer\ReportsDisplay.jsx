import React from 'react';
import './ReportsDisplay.css';

const ReportsDisplay = ({ reports }) => {
  if (!reports || !Array.isArray(reports) || reports.length === 0) {
    return null;
  }

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return '✅';
      case 'in_progress':
        return '🔄';
      case 'draft':
        return '📝';
      case 'pending':
        return '⏳';
      default:
        return '📄';
    }
  };

  const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'status-completed';
      case 'in_progress':
        return 'status-progress';
      case 'draft':
        return 'status-draft';
      case 'pending':
        return 'status-pending';
      default:
        return 'status-default';
    }
  };

  const getTypeIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'sales':
        return '💰';
      case 'marketing':
        return '📢';
      case 'financial':
        return '💼';
      case 'customer':
        return '👥';
      default:
        return '📊';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="reports-display">
      <h3 className="reports-title">📋 Reports</h3>
      <div className="reports-list">
        {reports.map((report, index) => (
          <div key={report.id || index} className="report-card">
            <div className="report-header">
              <div className="report-type">
                <span className="type-icon">{getTypeIcon(report.type)}</span>
                <span className="type-text">{report.type}</span>
              </div>
              <div className={`report-status ${getStatusClass(report.status)}`}>
                <span className="status-icon">{getStatusIcon(report.status)}</span>
                <span className="status-text">{report.status}</span>
              </div>
            </div>
            <div className="report-title">{report.title}</div>
            {report.date && (
              <div className="report-date">📅 {formatDate(report.date)}</div>
            )}
            {report.id && (
              <div className="report-id">ID: {report.id}</div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReportsDisplay;
