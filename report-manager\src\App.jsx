import { useState, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import ChatWindow from './components/ChatWindow';
import Login from './components/Login/Login';
import authService from './services/authService';
import './App.css';

function App() {
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [isAuthLoading, setIsAuthLoading] = useState(true);

  // Existing conversation state
  const [conversations, setConversations] = useState([
    {
      id: 1,
      title: 'New Chat',
      messages: []
    }
  ]);
  const [activeConversation, setActiveConversation] = useState(1);

  // Initialize authentication on app load
  useEffect(() => {
    initializeAuth();
    handleOAuthRedirect();
  }, []);

  // Handle OAuth redirect
  const handleOAuthRedirect = () => {
    // Check URL parameters for authorization code
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const error = urlParams.get('error');

    // Check URL hash for implicit flow tokens
    const hash = window.location.hash;
    const hashParams = new URLSearchParams(hash.substring(1));
    const accessToken = hashParams.get('access_token');
    const idToken = hashParams.get('id_token');

    if (error) {
      console.error('OAuth error:', error);
      // Clear URL parameters and hash
      window.history.replaceState({}, document.title, window.location.pathname);
      return;
    }

    if (accessToken && idToken) {
      console.log('OAuth tokens received (implicit flow)');
      // Handle implicit flow tokens
      authService.handleImplicitTokens(accessToken, idToken)
        .then(user => {
          console.log('OAuth login successful:', user);
          setIsAuthenticated(true);
          setCurrentUser(user);
          setIsAuthLoading(false);
          // Clear URL hash
          window.history.replaceState({}, document.title, window.location.pathname);
        })
        .catch(error => {
          console.error('OAuth token handling failed:', error);
          setIsAuthLoading(false);
          // Clear URL hash
          window.history.replaceState({}, document.title, window.location.pathname);
        });
    } else if (code) {
      console.log('OAuth code received:', code);
      // Handle the authorization code
      authService.handleAuthCode(code)
        .then(user => {
          console.log('OAuth login successful:', user);
          setIsAuthenticated(true);
          setCurrentUser(user);
          setIsAuthLoading(false);
          // Clear URL parameters
          window.history.replaceState({}, document.title, window.location.pathname);
        })
        .catch(error => {
          console.error('OAuth code handling failed:', error);
          setIsAuthLoading(false);
          // Clear URL parameters
          window.history.replaceState({}, document.title, window.location.pathname);
        });
    }
  };

  // Listen for auth state changes
  useEffect(() => {
    const handleAuthStateChange = (event) => {
      const { isAuthenticated: authStatus, user } = event.detail;
      setIsAuthenticated(authStatus);
      setCurrentUser(user);
      setIsAuthLoading(false);
    };

    window.addEventListener('authStateChange', handleAuthStateChange);

    return () => {
      window.removeEventListener('authStateChange', handleAuthStateChange);
    };
  }, []);

  const initializeAuth = async () => {
    try {
      setIsAuthLoading(true);
      await authService.initialize();

      // Check if user is already authenticated
      const existingUser = await authService.checkExistingAuth();
      if (existingUser) {
        setIsAuthenticated(true);
        setCurrentUser(existingUser);
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
    } finally {
      setIsAuthLoading(false);
    }
  };

  const handleLoginSuccess = (user) => {
    setIsAuthenticated(true);
    setCurrentUser(user);
    setIsAuthLoading(false);
  };

  const handleSignOut = async () => {
    try {
      await authService.signOut();
      setIsAuthenticated(false);
      setCurrentUser(null);
      // Optionally clear conversations or reset app state
      console.log('User signed out successfully');
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };

  const handleNewChat = () => {
    const newId = Date.now();
    const newConv = { 
      id: newId, 
      title: 'New Chat', 
      messages: [] 
    };
    setConversations([...conversations, newConv]);
    setActiveConversation(newId);
  };

  const handleSendMessage = (messageData, messageType) => {
    console.log('handleSendMessage called:', { messageData, messageType });

    const updatedConversations = conversations.map(conv => {
      if (conv.id === activeConversation) {
        let newMessage;

        if (typeof messageData === 'string') {
          // Handle simple string messages (user messages)
          newMessage = {
            type: messageType,
            text: messageData,
            timestamp: new Date(),
            agents_used: [],
            success: true,
            isStructured: false
          };
          console.log('Created user message:', newMessage);
        } else if (messageData && typeof messageData === 'object' && messageData.text && messageType === 'user') {
          // Handle structured user messages (from ChatWindow)
          newMessage = {
            type: messageType,
            text: messageData.text,
            timestamp: messageData.timestamp || new Date(),
            agents_used: messageData.agents_used || [],
            success: messageData.success !== undefined ? messageData.success : true,
            isStructured: messageData.isStructured || false,
            originalQuery: messageData.originalQuery
          };
          console.log('Created structured user message:', newMessage);
        } else {
          // Handle complex message objects (assistant responses)
          newMessage = {
            type: messageType,
            text: typeof messageData.text === 'string' ? messageData.text : JSON.stringify(messageData.text || messageData),
            timestamp: messageData.timestamp || new Date(),
            agents_used: Array.isArray(messageData.agents_used) ? messageData.agents_used : [],
            success: messageData.success,
            isStructured: messageData.isStructured || false,
            originalQuery: messageData.originalQuery,
            downloadInfo: messageData.downloadInfo
          };
        }

        console.log('New message created:', newMessage);

        const newMessages = [...conv.messages, newMessage];
        const updatedConv = { ...conv, messages: newMessages };

        console.log('Updated conversation:', updatedConv);

        return updatedConv;
      }
      return conv;
    });

    console.log('Setting updated conversations:', updatedConversations);
    setConversations(updatedConversations);
  };



  // Show loading screen while initializing auth
  if (isAuthLoading) {
    return (
      <div className="app-loading">
        <div className="loading-container">
          <div className="app-logo">📊</div>
          <h1>Report Manager</h1>
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          <p>Initializing...</p>
        </div>
      </div>
    );
  }

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return <Login onLoginSuccess={handleLoginSuccess} />;
  }

  // Show main app if authenticated
  const currentConversation = conversations.find(conv => conv.id === activeConversation);

  console.log('App render - activeConversation:', activeConversation);
  console.log('App render - conversations:', conversations);
  console.log('App render - currentConversation:', currentConversation);
  console.log('App render - currentConversation messages:', currentConversation?.messages);

  return (
    <div className="app">
      <Sidebar
        conversations={conversations}
        activeConversation={activeConversation}
        onNewChat={handleNewChat}
        onSelectConversation={setActiveConversation}
        user={currentUser}
        onSignOut={handleSignOut}
      />
      <ChatWindow
        conversation={currentConversation}
        onSendMessage={handleSendMessage}
        user={currentUser}
      />
    </div>
  );
}

export default App;


