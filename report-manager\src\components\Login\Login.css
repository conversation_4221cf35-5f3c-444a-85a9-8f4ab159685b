/* Login Component Styles */
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  box-sizing: border-box;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 40px;
  width: 100%;
  max-width: 420px;
  text-align: center;
  margin: auto;
  position: relative;
}

/* Header */
.login-header {
  margin-bottom: 32px;
}

.app-logo {
  font-size: 48px;
  margin-bottom: 16px;
  display: inline-block;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.app-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.app-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* Content */
.login-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Benefits */
.login-benefits {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 8px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  text-align: left;
}

.benefit-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
}

.error-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.error-text {
  font-size: 14px;
  font-weight: 500;
  text-align: left;
}

/* Google Sign-in Button */
.google-signin-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 14px 24px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.google-signin-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.google-signin-btn:active {
  transform: translateY(0);
}

.google-signin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.google-signin-btn.loading {
  border-color: #6b7280;
}

.google-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Button Spinner */
.btn-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Footer */
.login-footer {
  margin-top: 8px;
}

.privacy-text {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }

  .login-card {
    padding: 24px;
    max-width: 100%;
    margin: 0;
  }

  .app-title {
    font-size: 24px;
  }

  .app-subtitle {
    font-size: 14px;
  }

  .benefit-item {
    padding: 10px 12px;
  }

  .benefit-text {
    font-size: 13px;
  }
}

@media (max-width: 360px) {
  .login-container {
    padding: 12px;
  }

  .login-card {
    padding: 20px;
  }

  .app-logo {
    font-size: 40px;
  }

  .app-title {
    font-size: 22px;
  }
}
