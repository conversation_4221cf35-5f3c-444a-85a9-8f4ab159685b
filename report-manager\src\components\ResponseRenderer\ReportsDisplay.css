.reports-display {
  margin-bottom: 20px;
}

.reports-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.reports-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.report-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.report-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.report-type {
  display: flex;
  align-items: center;
  gap: 6px;
}

.type-icon {
  font-size: 16px;
}

.type-text {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.report-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-progress {
  background: #dbeafe;
  color: #1e40af;
}

.status-draft {
  background: #fef3c7;
  color: #92400e;
}

.status-pending {
  background: #f3e8ff;
  color: #7c3aed;
}

.status-default {
  background: #f3f4f6;
  color: #374151;
}

.report-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.report-date {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.report-id {
  font-size: 12px;
  color: #9ca3af;
  font-family: 'Courier New', monospace;
}
