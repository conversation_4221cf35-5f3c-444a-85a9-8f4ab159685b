"""
Excel utilities for Report Manager Orchestrator

This module provides utilities for generating Excel files from streamed data.
"""

import io
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

try:
    import pandas as pd
    import openpyxl
    from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
    from openpyxl.utils.dataframe import dataframe_to_rows
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logger.warning("Excel dependencies not available. Install openpyxl and pandas for Excel export.")


class ExcelGenerator:
    """
    Generates Excel files from structured data
    """
    
    def __init__(self):
        """Initialize the Excel generator"""
        if not EXCEL_AVAILABLE:
            raise ImportError("Excel dependencies not available. Install openpyxl and pandas.")
    
    def create_workbook_from_stream_data(self, stream_chunks: List[Dict[str, Any]]) -> bytes:
        """
        Create an Excel workbook from streamed data chunks
        
        Args:
            stream_chunks: List of data chunks from the stream
            
        Returns:
            Excel file as bytes
        """
        try:
            workbook = openpyxl.Workbook()
            
            # Remove default sheet
            workbook.remove(workbook.active)
            
            # Group chunks by sheet
            sheets_data = {}
            metadata = {}
            
            for chunk in stream_chunks:
                if chunk.get("type") == "headers":
                    sheet_name = chunk.get("sheet_name", "Sheet1")
                    if sheet_name not in sheets_data:
                        sheets_data[sheet_name] = {
                            "headers": chunk.get("headers", []),
                            "rows": []
                        }
                    metadata = chunk.get("metadata", {})
                
                elif chunk.get("type") == "rows":
                    sheet_name = chunk.get("sheet_name", "Sheet1")
                    if sheet_name in sheets_data:
                        sheets_data[sheet_name]["rows"].extend(chunk.get("rows", []))
            
            # Create sheets
            for sheet_name, data in sheets_data.items():
                # Ensure sheet name is valid for Excel
                safe_sheet_name = sheet_name[:31]  # Excel limit
                safe_sheet_name = safe_sheet_name.replace('[', '').replace(']', '').replace('*', '').replace('?', '').replace('/', '').replace('\\', '')

                worksheet = workbook.create_sheet(title=safe_sheet_name)

                # Add headers
                headers = data.get("headers", [])
                if headers:
                    for col, header in enumerate(headers, 1):
                        cell = worksheet.cell(row=1, column=col, value=str(header))
                        cell.font = Font(bold=True)
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                        cell.alignment = Alignment(horizontal="center")

                # Add data rows
                rows = data.get("rows", [])
                for row_idx, row_data in enumerate(rows, 2):
                    if isinstance(row_data, dict):
                        # Convert dict to list based on headers
                        row_values = [row_data.get(header, "") for header in headers]
                    elif isinstance(row_data, list):
                        row_values = row_data
                    else:
                        row_values = [str(row_data)]

                    for col, value in enumerate(row_values, 1):
                        # Ensure value is serializable
                        try:
                            cell_value = value if value is not None else ""
                            if isinstance(cell_value, (list, dict)):
                                cell_value = str(cell_value)
                            worksheet.cell(row=row_idx, column=col, value=cell_value)
                        except Exception as e:
                            worksheet.cell(row=row_idx, column=col, value=str(value))
                
                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # Ensure we have at least one sheet
            if len(workbook.worksheets) == 0:
                # Create a default sheet with message
                default_sheet = workbook.create_sheet(title="No Data")
                default_sheet.cell(row=1, column=1, value="No data available")
                default_sheet.cell(row=2, column=1, value="Please try a different query")

            # Add metadata sheet if available
            if metadata:
                meta_sheet = workbook.create_sheet(title="Metadata")
                meta_sheet.cell(row=1, column=1, value="Property").font = Font(bold=True)
                meta_sheet.cell(row=1, column=2, value="Value").font = Font(bold=True)

                row = 2
                for key, value in metadata.items():
                    meta_sheet.cell(row=row, column=1, value=str(key))
                    meta_sheet.cell(row=row, column=2, value=str(value))
                    row += 1
            
            # Save to bytes
            excel_buffer = io.BytesIO()
            workbook.save(excel_buffer)
            excel_buffer.seek(0)
            
            return excel_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Error creating Excel workbook: {e}")
            raise
    
    def create_simple_excel(self, data: Dict[str, Any], filename: str = None) -> bytes:
        """
        Create a simple Excel file from structured data
        
        Args:
            data: Structured data dictionary
            filename: Optional filename for the Excel file
            
        Returns:
            Excel file as bytes
        """
        try:
            workbook = openpyxl.Workbook()
            workbook.remove(workbook.active)
            
            sheets = data.get("sheets", [])
            if not sheets:
                # Create a simple sheet from the data
                sheets = [{
                    "name": "Data",
                    "headers": ["Key", "Value"],
                    "rows": [[k, str(v)] for k, v in data.items()]
                }]
            
            for sheet_data in sheets:
                worksheet = workbook.create_sheet(title=sheet_data.get("name", "Sheet"))
                
                # Add headers
                headers = sheet_data.get("headers", [])
                for col, header in enumerate(headers, 1):
                    cell = worksheet.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                
                # Add rows
                rows = sheet_data.get("rows", [])
                for row_idx, row_data in enumerate(rows, 2):
                    if isinstance(row_data, dict):
                        row_values = [row_data.get(header, "") for header in headers]
                    elif isinstance(row_data, list):
                        row_values = row_data
                    else:
                        row_values = [str(row_data)]
                    
                    for col, value in enumerate(row_values, 1):
                        worksheet.cell(row=row_idx, column=col, value=value)
            
            # Save to bytes
            excel_buffer = io.BytesIO()
            workbook.save(excel_buffer)
            excel_buffer.seek(0)
            
            return excel_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Error creating simple Excel file: {e}")
            raise


def create_excel_from_chunks(chunks: List[Dict[str, Any]]) -> bytes:
    """
    Convenience function to create Excel from stream chunks
    
    Args:
        chunks: List of stream data chunks
        
    Returns:
        Excel file as bytes
    """
    if not EXCEL_AVAILABLE:
        raise ImportError("Excel dependencies not available")
    
    generator = ExcelGenerator()
    return generator.create_workbook_from_stream_data(chunks)


def is_excel_available() -> bool:
    """
    Check if Excel generation is available
    
    Returns:
        True if Excel dependencies are installed
    """
    return EXCEL_AVAILABLE
