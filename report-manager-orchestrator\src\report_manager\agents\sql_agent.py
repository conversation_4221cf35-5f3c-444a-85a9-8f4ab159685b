"""
SQL Agent for Text-to-SQL Conversion

This agent converts natural language queries into SQL statements
using database schema information and AI-powered parsing.
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from loguru import logger

from .base import BaseAgent
from ..core.context import User<PERSON>ontext, TaskType
from ..core.result import QueryResult
from ..database.config_manager import DatabaseConfigManager, DataSource
from ..database.schema_discovery import DynamicSchemaDiscovery


@dataclass
class SQLQuery:
    """Represents a generated SQL query with metadata"""
    sql: str
    datasource: str
    tables: List[str]
    columns: List[str]
    parameters: Dict[str, Any]
    confidence: float
    explanation: str


class SQLAgent(BaseAgent):
    """
    Agent responsible for converting natural language to SQL queries
    """
    
    def __init__(self, llm_client, config_manager: DatabaseConfigManager, schema_discovery: DynamicSchemaDiscovery):
        """
        Initialize the SQL agent

        Args:
            llm_client: Language model client for AI-powered parsing
            config_manager: Database configuration manager
            schema_discovery: Dynamic schema discovery service
        """
        super().__init__("sql_agent", ["QUERY", "DATA_ANALYSIS"])
        self.llm_client = llm_client
        self.config_manager = config_manager
        self.schema_discovery = schema_discovery
        
        # SQL generation patterns for natural language processing
        self.query_patterns = {
            'aggregation': ['total', 'sum', 'count', 'average', 'avg', 'max', 'min'],
            'filtering': ['where', 'filter', 'only', 'specific', 'particular'],
            'grouping': ['by', 'group', 'category', 'department', 'type'],
            'sorting': ['top', 'bottom', 'highest', 'lowest', 'best', 'worst', 'order'],
            'time_based': ['today', 'yesterday', 'week', 'month', 'year', 'quarter', 'daily', 'monthly']
        }
        
        logger.info("SQL Agent initialized")
    
    async def process(self, context: UserContext) -> QueryResult:
        """
        Process a user query and convert it to SQL
        
        Args:
            context: User context containing the query
            
        Returns:
            QueryResult with generated SQL query
        """
        try:
            logger.info(f"SQL Agent processing query: {context.intent}")
            
            # Find relevant data sources and tables
            relevant_datasources = self.config_manager.find_relevant_datasources(context.intent)
            
            if not relevant_datasources:
                return QueryResult(
                    success=False,
                    error="No relevant data sources found for this query",
                    query_type="sql_generation"
                )
            
            # Generate SQL for the most relevant data source
            primary_datasource = relevant_datasources[0]
            relevant_tables = self.config_manager.find_relevant_tables(context.intent, primary_datasource)
            
            if not relevant_tables:
                # Use all tables if no specific tables identified
                datasource = self.config_manager.get_data_source(primary_datasource)
                relevant_tables = list(datasource.tables.keys()) if datasource else []
            
            # Generate SQL query
            sql_query = await self._generate_sql_query(
                context.intent, 
                primary_datasource, 
                relevant_tables
            )
            
            if not sql_query:
                return QueryResult(
                    success=False,
                    error="Failed to generate SQL query",
                    query_type="sql_generation"
                )
            
            return QueryResult(
                success=True,
                data={
                    "sql_query": sql_query.sql,
                    "datasource": sql_query.datasource,
                    "tables": sql_query.tables,
                    "columns": sql_query.columns,
                    "parameters": sql_query.parameters,
                    "confidence": sql_query.confidence,
                    "explanation": sql_query.explanation
                },
                query_type="sql_generation",
                metadata={
                    "relevant_datasources": relevant_datasources,
                    "relevant_tables": relevant_tables
                }
            )
            
        except Exception as e:
            logger.error(f"Error in SQL Agent: {e}")
            return QueryResult(
                success=False,
                error=str(e),
                query_type="sql_generation"
            )
    
    async def _generate_sql_query(self, query_text: str, datasource_name: str, tables: List[str]) -> Optional[SQLQuery]:
        """
        Generate SQL query using AI and pattern matching
        
        Args:
            query_text: Natural language query
            datasource_name: Target data source
            tables: Relevant tables
            
        Returns:
            Generated SQL query or None if failed
        """
        try:
            # Get data source and table information
            datasource = self.config_manager.get_data_source(datasource_name)
            if not datasource:
                logger.error(f"Data source not found: {datasource_name}")
                return None
            
            # Build schema context for AI
            schema_context = self._build_schema_context(datasource, tables)
            
            # Check for predefined query templates first
            template_query = self._check_query_templates(query_text, datasource_name)
            if template_query:
                return template_query
            
            # Use AI to generate SQL
            ai_query = await self._generate_ai_sql(query_text, schema_context, datasource_name, tables)
            if ai_query:
                return ai_query
            
            # Fallback to pattern-based generation
            pattern_query = self._generate_pattern_sql(query_text, datasource, tables)
            return pattern_query
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {e}")
            return None
    
    def _build_schema_context(self, datasource: DataSource, tables: List[str]) -> str:
        """
        Build schema context for AI SQL generation
        
        Args:
            datasource: Data source information
            tables: List of relevant tables
            
        Returns:
            Schema context string
        """
        context_parts = [
            f"Database: {datasource.name} ({datasource.type})",
            f"Description: {datasource.description}",
            "",
            "Available Tables:"
        ]
        
        for table_name in tables:
            if table_name in datasource.tables:
                table_info = datasource.tables[table_name]
                context_parts.append(f"\nTable: {table_name}")
                context_parts.append(f"Description: {table_info.description}")
                context_parts.append("Columns:")
                
                for col_name, col_info in table_info.columns.items():
                    context_parts.append(f"  - {col_name} ({col_info.type}): {col_info.description}")
        
        return "\n".join(context_parts)
    
    def _check_query_templates(self, query_text: str, datasource_name: str) -> Optional[SQLQuery]:
        """
        Check if query matches predefined templates
        
        Args:
            query_text: Natural language query
            datasource_name: Target data source
            
        Returns:
            SQL query if template matches, None otherwise
        """
        query_lower = query_text.lower()
        templates = self.config_manager.get_query_templates()
        
        for template_name, template_config in templates.items():
            if template_config.get('datasource') != datasource_name:
                continue
            
            # Simple keyword matching for templates
            template_keywords = template_name.replace('_', ' ').split()
            if all(keyword in query_lower for keyword in template_keywords):
                sql = template_config.get('sql', '')
                
                # Extract parameters (simplified)
                parameters = {}
                for param in template_config.get('parameters', []):
                    if param == 'limit':
                        # Extract number from query
                        numbers = re.findall(r'\d+', query_text)
                        parameters[param] = numbers[0] if numbers else '10'
                    elif param in ['start_date', 'end_date']:
                        # For demo, use default dates
                        parameters[param] = '2024-01-01' if 'start' in param else '2024-12-31'
                
                # Replace parameters in SQL
                formatted_sql = sql.format(**parameters)
                
                return SQLQuery(
                    sql=formatted_sql,
                    datasource=datasource_name,
                    tables=[],  # Extract from SQL if needed
                    columns=[],
                    parameters=parameters,
                    confidence=0.9,
                    explanation=f"Used predefined template: {template_name}"
                )
        
        return None
    
    async def _generate_ai_sql(self, query_text: str, schema_context: str, datasource_name: str, tables: List[str]) -> Optional[SQLQuery]:
        """
        Generate SQL using AI language model with specific patterns for eInvoicing

        Args:
            query_text: Natural language query
            schema_context: Database schema information
            datasource_name: Target data source
            tables: Relevant tables

        Returns:
            Generated SQL query or None
        """
        try:
            # EXACT MATCH for specific eInvoicing tax code patterns
            query_lower = query_text.lower()

            # Handle eInvoicing tax code requests with EXACT matching
            if ("einvoicing" in query_lower and "tax" in query_lower) or \
               ("tax" in query_lower and "code" in query_lower and any(word in query_lower for word in ["einvoicing", "invoice"])):
                # Use SQLite table name (no schema support)
                sql = "SELECT * FROM eInvoicing_TaxCodeLookup ORDER BY TaxCode"
                logger.info(f"EXACT MATCH: eInvoicing tax code pattern detected in: {query_text}")
                return SQLQuery(
                    sql=sql,
                    datasource=datasource_name,
                    tables=["eInvoicing_TaxCodeLookup"],
                    columns=["TaxCode", "TaxRate", "Description", "TaxType", "IsActive"],
                    parameters={},
                    confidence=1.0,  # Maximum confidence for exact match
                    explanation="EXACT MATCH: eInvoicing tax code request - returning ONLY tax codes from TaxCodeLookup"
                )

            # Handle general tax code requests
            if "tax" in query_lower and "code" in query_lower:
                if "einvoicing" in schema_context.lower():
                    sql = "SELECT * FROM eInvoicing_TaxCodeLookup ORDER BY TaxCode"
                else:
                    sql = "SELECT * FROM TaxCodes ORDER BY TaxCode"

                return SQLQuery(
                    sql=sql,
                    datasource=datasource_name,
                    tables=["eInvoicing_TaxCodeLookup" if "einvoicing" in schema_context.lower() else "TaxCodes"],
                    columns=["*"],
                    parameters={},
                    confidence=0.9,
                    explanation="Matched tax code pattern"
                )

            # Handle employee queries for Excel database
            if datasource_name == "employee_db" or "employee" in query_lower or "staff" in query_lower or "hr" in query_lower:
                # For Excel databases, generate more intelligent SELECT queries
                sql = "SELECT * FROM Employees"
                where_conditions = []
                order_by = ""

                # Handle department filtering
                if "department" in query_lower:
                    if "hr" in query_lower:
                        where_conditions.append("Department = 'HR'")
                    elif "engineering" in query_lower:
                        where_conditions.append("Department = 'Engineering'")
                    elif "sales" in query_lower:
                        where_conditions.append("Department = 'Sales'")
                    elif "marketing" in query_lower:
                        where_conditions.append("Department = 'Marketing'")
                    elif "finance" in query_lower:
                        where_conditions.append("Department = 'Finance'")
                    elif "operations" in query_lower:
                        where_conditions.append("Department = 'Operations'")
                    else:
                        order_by = "ORDER BY Department"

                # Handle years of service filtering
                if "year" in query_lower and "service" in query_lower:
                    if "greater than" in query_lower or "more than" in query_lower or ">" in query_lower:
                        # Extract the number
                        numbers = re.findall(r'\d+', query_text)
                        if numbers:
                            years = numbers[0]
                            where_conditions.append(f"YearsOfService > {years}")
                    elif "less than" in query_lower or "<" in query_lower:
                        numbers = re.findall(r'\d+', query_text)
                        if numbers:
                            years = numbers[0]
                            where_conditions.append(f"YearsOfService < {years}")
                    elif "equal" in query_lower or "=" in query_lower:
                        numbers = re.findall(r'\d+', query_text)
                        if numbers:
                            years = numbers[0]
                            where_conditions.append(f"YearsOfService = {years}")

                # Handle salary filtering
                if "salary" in query_lower:
                    if "greater than" in query_lower or "more than" in query_lower or ">" in query_lower:
                        numbers = re.findall(r'\d+', query_text)
                        if numbers:
                            salary = numbers[0]
                            where_conditions.append(f"Salary > {salary}")
                    elif "less than" in query_lower or "<" in query_lower:
                        numbers = re.findall(r'\d+', query_text)
                        if numbers:
                            salary = numbers[0]
                            where_conditions.append(f"Salary < {salary}")
                    else:
                        order_by = "ORDER BY Salary DESC"

                # Handle location filtering
                if "location" in query_lower:
                    if "new york" in query_lower:
                        where_conditions.append("Location = 'New York'")
                    elif "san francisco" in query_lower:
                        where_conditions.append("Location = 'San Francisco'")
                    elif "chicago" in query_lower:
                        where_conditions.append("Location = 'Chicago'")
                    elif "austin" in query_lower:
                        where_conditions.append("Location = 'Austin'")
                    else:
                        order_by = "ORDER BY Location"

                # Handle name filtering
                if ("name" in query_lower and ("starts with" in query_lower or "begins with" in query_lower or "starting with" in query_lower or "start with" in query_lower)):
                    # Extract the letter/pattern after "starts with" or "begins with"

                    # Look for patterns like "starts with R", "begins with A", "start with letter R", etc.
                    # Order matters - more specific patterns first!
                    start_patterns = [
                        r"start with letter ([A-Za-z])",
                        r"starts with letter ([A-Za-z])",
                        r"begin with letter ([A-Za-z])",
                        r"begins with letter ([A-Za-z])",
                        r"starts with ([A-Za-z])",
                        r"begins with ([A-Za-z])",
                        r"starting with ([A-Za-z])",
                        r"start with ([A-Za-z])",
                        r"begin with ([A-Za-z])"
                    ]

                    letter = None
                    for pattern in start_patterns:
                        match = re.search(pattern, query_text, re.IGNORECASE)
                        if match:
                            letter = match.group(1).upper()
                            break

                    if letter:
                        # Determine which name column to use
                        if "first name" in query_lower or "firstname" in query_lower:
                            # Use FirstName column if it exists, otherwise extract from FullName
                            where_conditions.append(f"FirstName LIKE '{letter}%'")
                        elif "last name" in query_lower or "lastname" in query_lower:
                            where_conditions.append(f"LastName LIKE '{letter}%'")
                        else:
                            # Default to FullName or FirstName
                            where_conditions.append(f"FullName LIKE '{letter}%'")

                # Handle position/title filtering
                if "position" in query_lower or "title" in query_lower or "role" in query_lower:
                    if "manager" in query_lower:
                        where_conditions.append("Position LIKE '%Manager%'")
                    elif "director" in query_lower:
                        where_conditions.append("Position LIKE '%Director%'")
                    elif "analyst" in query_lower:
                        where_conditions.append("Position LIKE '%Analyst%'")
                    elif "engineer" in query_lower:
                        where_conditions.append("Position LIKE '%Engineer%'")
                    elif "senior" in query_lower:
                        where_conditions.append("Position LIKE 'Senior%'")
                    elif "junior" in query_lower:
                        where_conditions.append("Position LIKE 'Junior%'")

                # Handle status filtering
                if "status" in query_lower:
                    if "active" in query_lower:
                        where_conditions.append("Status = 'Active'")
                    elif "inactive" in query_lower:
                        where_conditions.append("Status = 'Inactive'")
                    elif "on leave" in query_lower or "leave" in query_lower:
                        where_conditions.append("Status = 'On Leave'")
                    elif "terminated" in query_lower:
                        where_conditions.append("Status = 'Terminated'")
                    elif "pending" in query_lower:
                        where_conditions.append("Status = 'Pending'")

                # Handle active/inactive without explicit "status" keyword
                elif ("active" in query_lower and "employee" in query_lower) or "active employees" in query_lower:
                    where_conditions.append("Status = 'Active'")
                elif ("inactive" in query_lower and "employee" in query_lower) or "inactive employees" in query_lower:
                    where_conditions.append("Status = 'Inactive'")

                # Build the final SQL query
                if where_conditions:
                    sql += " WHERE " + " AND ".join(where_conditions)

                if order_by and not where_conditions:
                    sql += " " + order_by
                elif not order_by and not where_conditions:
                    sql += " ORDER BY EmployeeID"

                return SQLQuery(
                    sql=sql,
                    datasource=datasource_name,
                    tables=["Employees"],
                    columns=["*"],
                    parameters={},
                    confidence=0.9,
                    explanation=f"Generated employee query with conditions: {where_conditions if where_conditions else 'none'}"
                )

            # Construct prompt for AI for other queries
            prompt = f"""
Convert the following natural language query to SQL based on the provided database schema.

Database Schema:
{schema_context}

Natural Language Query: {query_text}

Special Instructions:
- For eInvoicing tax codes, use: SELECT * FROM eInvoicing_TaxCodeLookup
- For general tax codes, use: SELECT * FROM TaxCodes
- Generate only SELECT statements
- Use proper table joins when needed
- Include appropriate WHERE clauses for filtering
- Use aggregate functions when asking for totals, counts, etc.
- Return only the SQL query, no explanations

SQL Query:
"""

            # Call AI model
            response = await self.llm_client.generate_response(prompt)

            if not response or not response.strip():
                logger.warning("AI returned empty SQL response")
                return None

            # Clean and validate SQL
            sql = self._clean_sql(response)

            if not self._validate_sql(sql):
                logger.warning(f"Generated SQL failed validation: {sql}")
                return None

            return SQLQuery(
                sql=sql,
                datasource=datasource_name,
                tables=tables,
                columns=self._extract_columns_from_sql(sql),
                parameters={},
                confidence=0.8,
                explanation="Generated using AI language model"
            )

        except Exception as e:
            logger.error(f"Error in AI SQL generation: {e}")
            return None
    
    def _generate_pattern_sql(self, query_text: str, datasource: DataSource, tables: List[str]) -> Optional[SQLQuery]:
        """
        Generate SQL using pattern matching as fallback
        
        Args:
            query_text: Natural language query
            datasource: Data source information
            tables: Relevant tables
            
        Returns:
            Generated SQL query or None
        """
        try:
            query_lower = query_text.lower()
            
            # Default to first table if available
            if not tables:
                return None
            
            primary_table = tables[0]
            table_info = datasource.tables.get(primary_table)
            
            if not table_info:
                return None
            
            # Build basic SELECT
            columns = ["*"]  # Default to all columns
            
            # Check for specific column requests
            for col_name in table_info.columns.keys():
                if col_name.lower() in query_lower:
                    if "*" in columns:
                        columns = []
                    columns.append(col_name)
            
            # Build WHERE clause
            where_conditions = []
            
            # Check for aggregation
            aggregation = None
            for agg_keyword in self.query_patterns['aggregation']:
                if agg_keyword in query_lower:
                    if agg_keyword in ['total', 'sum']:
                        # Find numeric columns
                        numeric_cols = [col for col, info in table_info.columns.items() 
                                      if info.type in ['integer', 'decimal', 'float']]
                        if numeric_cols:
                            aggregation = f"SUM({numeric_cols[0]})"
                            columns = [f"SUM({numeric_cols[0]}) as total"]
                    elif agg_keyword in ['count']:
                        aggregation = "COUNT(*)"
                        columns = ["COUNT(*) as count"]
                    break
            
            # Build SQL
            sql_parts = [f"SELECT {', '.join(columns)}"]
            sql_parts.append(f"FROM {primary_table}")
            
            if where_conditions:
                sql_parts.append(f"WHERE {' AND '.join(where_conditions)}")
            
            # Add LIMIT for safety
            sql_parts.append("LIMIT 100")
            
            sql = " ".join(sql_parts)
            
            return SQLQuery(
                sql=sql,
                datasource=datasource.name,
                tables=[primary_table],
                columns=columns,
                parameters={},
                confidence=0.6,
                explanation="Generated using pattern matching"
            )
            
        except Exception as e:
            logger.error(f"Error in pattern SQL generation: {e}")
            return None
    
    def _clean_sql(self, sql: str) -> str:
        """Clean and format SQL query"""
        # Remove markdown code blocks if present
        sql = re.sub(r'```sql\s*', '', sql)
        sql = re.sub(r'```\s*', '', sql)
        
        # Remove extra whitespace
        sql = ' '.join(sql.split())
        
        # Ensure it ends with semicolon
        sql = sql.rstrip(';') + ';'
        
        return sql
    
    def _validate_sql(self, sql: str) -> bool:
        """Basic SQL validation"""
        sql_upper = sql.upper()
        
        # Must start with SELECT
        if not sql_upper.strip().startswith('SELECT'):
            return False
        
        # Check for dangerous keywords
        dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                return False
        
        # Basic syntax check
        if 'FROM' not in sql_upper:
            return False
        
        return True
    
    def _extract_columns_from_sql(self, sql: str) -> List[str]:
        """Extract column names from SQL query"""
        try:
            # Simple regex to extract columns from SELECT clause
            select_match = re.search(r'SELECT\s+(.*?)\s+FROM', sql, re.IGNORECASE)
            if select_match:
                columns_str = select_match.group(1)
                if columns_str.strip() == '*':
                    return ['*']
                
                # Split by comma and clean
                columns = [col.strip() for col in columns_str.split(',')]
                return columns
        except Exception as e:
            logger.warning(f"Error extracting columns from SQL: {e}")
        
        return []
