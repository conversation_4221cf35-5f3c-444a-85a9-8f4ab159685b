"""
Report Manager Orchestrator

This module contains the main orchestrator that processes user text,
creates context with LLM, and coordinates agent calls.
"""

import asyncio
from typing import Dict, List, Any, Optional
import json
from loguru import logger

from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from .models import TaskType, UserContext, ContextResult, OrchestrationResult
from ..utils.io import IOHandler


class ReportManagerOrchestrator:
    """
    Main orchestrator that processes user text, creates context with LLM,
    and coordinates agent calls to fulfill user requests.
    """
    
    def __init__(self,
                 openai_api_key: str = None,
                 model_name: str = "gpt-4",
                 temperature: float = 0.1,
                 use_azure: bool = False,
                 azure_endpoint: str = None,
                 azure_deployment: str = None,
                 azure_api_version: str = "2024-11-01-preview"):
        """
        Initialize the orchestrator

        Args:
            openai_api_key: OpenAI API key for LLM access
            model_name: Name of the LLM model to use
            temperature: Temperature setting for LLM
            use_azure: Whether to use Azure OpenAI
            azure_endpoint: Azure OpenAI endpoint
            azure_deployment: Azure OpenAI deployment name
            azure_api_version: Azure OpenAI API version
        """
        # Initialize LLM based on configuration
        if use_azure or azure_endpoint:
            self.llm = AzureChatOpenAI(
                api_key=openai_api_key,
                azure_endpoint=azure_endpoint,
                azure_deployment=azure_deployment,
                api_version=azure_api_version,
                temperature=temperature
            )
            logger.info(f"Orchestrator initialized with Azure OpenAI: {azure_deployment}")
        else:
            self.llm = ChatOpenAI(
                api_key=openai_api_key,
                model=model_name,
                temperature=temperature
            )
            logger.info(f"Orchestrator initialized with OpenAI: {model_name}")
        
        # Initialize agents (import here to avoid circular imports)
        from ..agents.router_agent import RouterAgent
        from ..agents.query_agent import QueryAgent

        self.router_agent = RouterAgent()
        self.query_agent = QueryAgent()
        
        # Initialize utilities
        self.io_handler = IOHandler()
        
        logger.info(f"Orchestrator initialized with model: {model_name}")
    
    async def process_user_input(self, user_text: str) -> OrchestrationResult:
        """
        Main entry point for processing user input
        
        Args:
            user_text: Raw text input from user
            
        Returns:
            OrchestrationResult with the final result and metadata
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            logger.info(f"Processing user input: {user_text[:100]}...")
            
            # Step 1: Create context from user input using LLM
            context_result = await self.create_context(user_text)
            
            # Step 2: Route to appropriate agents based on context
            agents_to_use = await self.route_to_agents(context_result)
            
            # Step 3: Execute agent workflow
            result = await self.execute_agent_workflow(
                context_result.context, 
                agents_to_use
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return OrchestrationResult(
                success=True,
                result=result,
                context=context_result.context,
                agents_used=agents_to_use,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Error processing user input: {str(e)}")
            
            return OrchestrationResult(
                success=False,
                result=None,
                context=UserContext(
                    original_text=user_text,
                    intent="unknown",
                    task_type=TaskType.UNKNOWN,
                    entities=[],
                    parameters={},
                    confidence=0.0,
                    metadata={}
                ),
                agents_used=[],
                execution_time=execution_time,
                error=str(e)
            )
    
    async def create_context(self, user_text: str) -> ContextResult:
        """
        Use LLM to analyze user input and create rich context
        
        Args:
            user_text: Raw user input
            
        Returns:
            ContextResult with analyzed context and suggestions
        """
        system_prompt = """
        You are an expert at analyzing user requests for a report management system.
        Your job is to understand the user's intent and extract relevant information.
        
        Analyze the user input and provide:
        1. Intent: What the user wants to accomplish
        2. Task type: One of [query, report_generation, data_analysis, workflow, unknown]
        3. Entities: Important entities mentioned (names, dates, metrics, etc.)
        4. Parameters: Key-value pairs of parameters for the task
        5. Confidence: How confident you are in your analysis (0.0-1.0)
        6. Suggested agents: Which agents should handle this request
        
        Respond in JSON format with these fields:
        {
            "intent": "string",
            "task_type": "string", 
            "entities": ["list", "of", "entities"],
            "parameters": {"key": "value"},
            "confidence": 0.95,
            "reasoning": "explanation of your analysis",
            "suggested_agents": ["agent1", "agent2"]
        }
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"User input: {user_text}")
        ]
        
        response = await self.llm.ainvoke(messages)
        
        try:
            analysis = json.loads(response.content)
            
            context = UserContext(
                original_text=user_text,
                intent=analysis.get("intent", "unknown"),
                task_type=TaskType(analysis.get("task_type", "unknown")),
                entities=analysis.get("entities", []),
                parameters=analysis.get("parameters", {}),
                confidence=analysis.get("confidence", 0.0),
                metadata={"llm_analysis": analysis}
            )
            
            return ContextResult(
                context=context,
                reasoning=analysis.get("reasoning", ""),
                suggested_agents=analysis.get("suggested_agents", [])
            )
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Failed to parse LLM response: {e}")
            
            # Fallback context
            context = UserContext(
                original_text=user_text,
                intent="parse_error",
                task_type=TaskType.UNKNOWN,
                entities=[],
                parameters={},
                confidence=0.0,
                metadata={"error": str(e)}
            )
            
            return ContextResult(
                context=context,
                reasoning="Failed to parse LLM response",
                suggested_agents=["query_agent"]  # Default fallback
            )
    
    async def route_to_agents(self, context_result: ContextResult) -> List[str]:
        """
        Determine which agents to use based on context
        
        Args:
            context_result: Result from context creation
            
        Returns:
            List of agent names to use
        """
        # Use router agent to determine the best agents
        agents = await self.router_agent.route(
            context_result.context,
            context_result.suggested_agents
        )
        
        logger.info(f"Routing to agents: {agents}")
        return agents
    
    async def execute_agent_workflow(self, 
                                   context: UserContext, 
                                   agents: List[str]) -> Any:
        """
        Execute the workflow using the selected agents
        
        Args:
            context: User context
            agents: List of agents to use
            
        Returns:
            Result from agent execution
        """
        results = {}
        
        for agent_name in agents:
            logger.info(f"Executing agent: {agent_name}")
            
            if agent_name == "query_agent":
                result = await self.query_agent.process(context)
                results[agent_name] = result
            elif agent_name == "router_agent":
                # Router agent already used for routing
                continue
            else:
                logger.warning(f"Unknown agent: {agent_name}")
        
        # Combine results if multiple agents were used
        if len(results) == 1:
            return list(results.values())[0]
        else:
            return results
