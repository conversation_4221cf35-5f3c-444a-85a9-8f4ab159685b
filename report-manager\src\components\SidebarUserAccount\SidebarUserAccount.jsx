import React, { useState } from 'react';
import authService from '../../services/authService';
import './SidebarUserAccount.css';

const SidebarUserAccount = ({ user, onSignOut }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await authService.signOut();
      if (onSignOut) {
        onSignOut();
      }
    } catch (error) {
      console.error('Sign-out failed:', error);
    } finally {
      setIsSigningOut(false);
      setIsDropdownOpen(false);
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.sidebar-user-account')) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  if (!user) return null;

  return (
    <div className="sidebar-user-account">
      {/* Account Title Section */}
      <div className="account-title-section">
        <div className="account-header">
          <svg className="account-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <circle cx="12" cy="7" r="4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <h3 className="account-title">Account</h3>
        </div>
      </div>

      <button
        className="user-account-trigger"
        onClick={toggleDropdown}
        title={`Signed in as ${user.name || user.email}`}
      >
        <div className="user-avatar">
          {user.picture ? (
            <img
              src={user.picture}
              alt={user.name || user.email}
              className="avatar-img"
            />
          ) : (
            <div className="avatar-placeholder">
              {(user.name || user.email || 'U').charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        <div className="user-info">
          <div className="user-name">
            {user.name || user.email?.split('@')[0] || 'User'}
          </div>
          <div className="user-email">
            {user.email}
          </div>
        </div>

        {/* Quick Logout Button */}
        <button
          className={`quick-logout-btn ${isSigningOut ? 'loading' : ''}`}
          onClick={handleSignOut}
          disabled={isSigningOut}
          title="Sign Out"
        >
          {isSigningOut ? (
            <div className="logout-spinner-small"></div>
          ) : (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <polyline points="16,17 21,12 16,7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          )}
        </button>

        <div className={`dropdown-chevron ${isDropdownOpen ? 'open' : ''}`}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </button>

      {isDropdownOpen && (
        <div className="user-dropdown">
          <div className="dropdown-header">
            <div className="dropdown-avatar">
              {user.picture ? (
                <img 
                  src={user.picture} 
                  alt={user.name || user.email}
                  className="dropdown-avatar-img"
                />
              ) : (
                <div className="dropdown-avatar-placeholder">
                  {(user.name || user.email || 'U').charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <div className="dropdown-user-info">
              <div className="dropdown-user-name">
                {user.name || user.email?.split('@')[0] || 'User'}
              </div>
              <div className="dropdown-user-email">
                {user.email}
              </div>
            </div>
          </div>

          <div className="dropdown-divider"></div>

          <div className="dropdown-menu">
            <button 
              className="dropdown-item"
              onClick={() => {
                setIsDropdownOpen(false);
                // Add account management functionality here
              }}
            >
              <svg className="dropdown-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>Account Settings</span>
            </button>
            
            <button 
              className="dropdown-item"
              onClick={() => {
                setIsDropdownOpen(false);
                // Add preferences functionality here
              }}
            >
              <svg className="dropdown-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" strokeWidth="2"/>
              </svg>
              <span>Preferences</span>
            </button>
          </div>

          <div className="dropdown-divider"></div>

          <button 
            className={`dropdown-item logout-item ${isSigningOut ? 'loading' : ''}`}
            onClick={handleSignOut}
            disabled={isSigningOut}
          >
            {isSigningOut ? (
              <>
                <div className="logout-spinner"></div>
                <span>Signing out...</span>
              </>
            ) : (
              <>
                <svg className="dropdown-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <polyline points="16,17 21,12 16,7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>Sign Out</span>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default SidebarUserAccount;
