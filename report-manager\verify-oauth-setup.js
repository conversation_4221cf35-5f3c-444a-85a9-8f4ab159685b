// OAuth Setup Verification Script
// Run this with: node verify-oauth-setup.js

import fs from 'fs';
import path from 'path';

console.log('🔐 OAuth Setup Verification\n');

// Check if .env file exists
const envPath = '.env';
if (!fs.existsSync(envPath)) {
  console.log('❌ .env file not found');
  console.log('   Create a .env file in your project root');
  process.exit(1);
}

// Read .env file
const envContent = fs.readFileSync(envPath, 'utf8');
console.log('✅ .env file found');

// Check for required variables
const requiredVars = [
  'VITE_GOOGLE_CLIENT_ID',
  'VITE_REDIRECT_URI',
  'VITE_API_URL'
];

const missingVars = [];
const configuredVars = [];

requiredVars.forEach(varName => {
  if (envContent.includes(varName)) {
    const match = envContent.match(new RegExp(`${varName}=(.+)`));
    if (match && match[1] && !match[1].includes('your-actual-client-id')) {
      configuredVars.push(varName);
      console.log(`✅ ${varName} is configured`);
    } else {
      missingVars.push(varName);
      console.log(`⚠️  ${varName} needs to be configured`);
    }
  } else {
    missingVars.push(varName);
    console.log(`❌ ${varName} is missing`);
  }
});

console.log('\n📋 Setup Status:');
console.log(`✅ Configured: ${configuredVars.length}/${requiredVars.length}`);
console.log(`⚠️  Needs setup: ${missingVars.length}`);

if (missingVars.length > 0) {
  console.log('\n🔧 Next Steps:');
  console.log('1. Go to https://console.cloud.google.com/');
  console.log('2. Create OAuth credentials');
  console.log('3. Copy your Client ID');
  console.log('4. Update your .env file with the real Client ID');
  console.log('5. Restart your development server');
} else {
  console.log('\n🎉 OAuth setup is complete!');
  console.log('Your app should now use real Google OAuth instead of demo mode.');
}

console.log('\n📖 For detailed setup instructions, see: OAUTH_SETUP.md');
