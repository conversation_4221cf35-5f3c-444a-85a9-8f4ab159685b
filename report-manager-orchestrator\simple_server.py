#!/usr/bin/env python3
"""
Simple server for testing - no mock data, minimal dependencies
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from datetime import datetime
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="Report Manager - Clean Version",
    description="No mock data - database configuration required",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class QueryRequest(BaseModel):
    text: str

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Report Manager - Clean Version (No Mock Data)",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "mock_data_removed": True
    }

@app.post("/query/excel")
async def query_for_excel(request: QueryRequest):
    """Convert natural language to Excel data - NO MOCK DATA"""
    try:
        query_lower = request.text.lower()
        
        if "einvoicing" in query_lower and "tax" in query_lower:
            # Tax code query - require database configuration
            return {
                "success": False,
                "error": "Database connection required for tax code data",
                "metadata": {
                    "title": f"Report: {request.text}",
                    "generated_at": datetime.now().isoformat(),
                    "query": request.text,
                    "sql_query": "SELECT * FROM eInvoicing.TaxCodeLookup ORDER BY TaxCode",
                    "datasource": "invoicing_db",
                    "explanation": "Configure database connection in .env file"
                },
                "setup_instructions": {
                    "step1": "Add database credentials to .env file",
                    "step2": "Set INVOICING_DB_HOST, INVOICING_DB_NAME, INVOICING_DB_USER, INVOICING_DB_PASSWORD",
                    "step3": "Restart the server",
                    "step4": "Query will return real data from your database"
                },
                "note": "All mock data has been removed from this system"
            }
        else:
            # Other queries not supported without database
            return {
                "success": False,
                "error": f"Query type not supported without database configuration: {request.text}",
                "metadata": {
                    "title": f"Report: {request.text}",
                    "query": request.text
                },
                "supported_queries": [
                    "I need all einvoicing Tax code"
                ],
                "note": "Configure database for full functionality"
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/query")
async def regular_query(request: QueryRequest):
    """Regular query endpoint - NO MOCK DATA"""
    return {
        "success": False,
        "error": "Database configuration required",
        "result": {
            "message": "All mock data has been removed",
            "setup_required": "Configure database credentials in .env file",
            "query": request.text
        },
        "context": {},
        "agents_used": [],
        "execution_time": 0.0
    }

if __name__ == "__main__":
    print("🚀 Starting Clean Report Manager (No Mock Data)")
    print("🌐 Server: http://localhost:8000")
    print("📖 Docs: http://localhost:8000/docs")
    print("✅ All mock data removed")
    
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
