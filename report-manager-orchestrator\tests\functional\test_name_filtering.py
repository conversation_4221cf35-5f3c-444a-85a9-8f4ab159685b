#!/usr/bin/env python3
"""
Test Name Filtering Functionality
"""

import requests
import json

def test_name_filtering():
    """Test name-based filtering queries"""
    
    print("🧪 Testing Name Filtering Functionality")
    print("=" * 60)
    
    # Test queries with name filtering
    test_queries = [
        "all employee whose first name starts with R",
        "employees whose name starts with R",
        "find employees with first name starting with R",
        "show me employees whose first name begins with R",
        "list employees with names starting with R"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}️⃣ Testing: {query}")
        print("-" * 50)
        
        try:
            response = requests.post(
                "http://localhost:8009/query/excel-clean",
                json={"text": query},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    sheet = data['sheets'][0]
                    metadata = data['metadata']
                    
                    print(f"   ✅ Query Success!")
                    print(f"   📊 Rows returned: {len(sheet.get('rows', []))}")
                    print(f"   🗄️ Datasource: {metadata.get('datasource')}")
                    print(f"   🔍 Generated SQL: {metadata.get('sql_query')}")
                    print(f"   🎯 Confidence: {metadata.get('confidence')}")
                    
                    # Show sample results
                    rows = sheet.get('rows', [])
                    headers = sheet.get('headers', [])
                    
                    if rows:
                        print(f"   📄 Sample results:")
                        
                        # Find name column
                        name_idx = None
                        for idx, header in enumerate(headers):
                            if 'name' in header.lower() or 'fullname' in header.lower():
                                name_idx = idx
                                break
                        
                        # Show first 5 results
                        for j, row in enumerate(rows[:5]):
                            if name_idx is not None:
                                name = row[name_idx]
                                print(f"      {j+1}. {name}")
                            else:
                                print(f"      {j+1}. {row[:3]}...")  # Show first 3 columns
                        
                        # Verify filtering
                        if name_idx is not None:
                            names_with_r = [row for row in rows if row[name_idx] and str(row[name_idx]).startswith('R')]
                            names_without_r = [row for row in rows if row[name_idx] and not str(row[name_idx]).startswith('R')]
                            
                            print(f"   🎯 Filtering Verification:")
                            print(f"      Total rows: {len(rows)}")
                            print(f"      Names starting with R: {len(names_with_r)}")
                            print(f"      Names NOT starting with R: {len(names_without_r)}")
                            
                            if len(names_without_r) == 0 and len(names_with_r) > 0:
                                print(f"      ✅ PERFECT! All names start with R")
                            elif len(names_without_r) > 0:
                                print(f"      ❌ ISSUE: {len(names_without_r)} names don't start with R")
                                # Show examples of non-R names
                                for k, row in enumerate(names_without_r[:3]):
                                    print(f"         Example: {row[name_idx]}")
                            else:
                                print(f"      ⚠️  No names found starting with R")
                        
                        if len(rows) < 85:  # Total employees
                            print(f"   ✅ FILTERING APPLIED! ({len(rows)} < 85 total employees)")
                        else:
                            print(f"   ❌ NO FILTERING: Returned all {len(rows)} employees")
                    
                else:
                    print(f"   ❌ Query Failed: {data.get('error')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Name Filtering Test Complete!")

if __name__ == "__main__":
    test_name_filtering()
