#!/usr/bin/env python3
"""
Test Status Filtering Functionality
"""

import requests
import json

def test_status_filtering():
    """Test status-based filtering queries"""
    
    print("🧪 Testing Status Filtering Functionality")
    print("=" * 60)
    
    # Test queries with status filtering
    test_queries = [
        "All employee with status Active",
        "employees with Active status",
        "show me active employees",
        "find employees with status Active",
        "list all active employees"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}️⃣ Testing: {query}")
        print("-" * 50)
        
        try:
            response = requests.post(
                "http://localhost:8009/query/excel-clean",
                json={"text": query},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    sheet = data['sheets'][0]
                    metadata = data['metadata']
                    
                    print(f"   ✅ Query Success!")
                    print(f"   📊 Rows returned: {len(sheet.get('rows', []))}")
                    print(f"   🗄️ Datasource: {metadata.get('datasource')}")
                    print(f"   🔍 Generated SQL: {metadata.get('sql_query')}")
                    print(f"   🎯 Confidence: {metadata.get('confidence')}")
                    
                    # Show sample results
                    rows = sheet.get('rows', [])
                    headers = sheet.get('headers', [])
                    
                    if rows:
                        print(f"   📄 Sample results:")
                        
                        # Find status column
                        status_idx = None
                        name_idx = None
                        for idx, header in enumerate(headers):
                            if 'status' in header.lower():
                                status_idx = idx
                            if 'name' in header.lower() or 'fullname' in header.lower():
                                name_idx = idx
                        
                        # Show first 5 results
                        for j, row in enumerate(rows[:5]):
                            sample_info = []
                            if name_idx is not None:
                                sample_info.append(f"Name: {row[name_idx]}")
                            if status_idx is not None:
                                sample_info.append(f"Status: {row[status_idx]}")
                            else:
                                sample_info.append("Status: Column not found")
                            
                            print(f"      {j+1}. {', '.join(sample_info)}")
                        
                        # Verify filtering
                        if status_idx is not None:
                            active_employees = [row for row in rows if row[status_idx] and str(row[status_idx]).lower() == 'active']
                            inactive_employees = [row for row in rows if row[status_idx] and str(row[status_idx]).lower() != 'active']
                            
                            print(f"   🎯 Filtering Verification:")
                            print(f"      Total rows: {len(rows)}")
                            print(f"      Active employees: {len(active_employees)}")
                            print(f"      Non-active employees: {len(inactive_employees)}")
                            
                            if len(inactive_employees) == 0 and len(active_employees) > 0:
                                print(f"      ✅ PERFECT! All employees are Active")
                            elif len(inactive_employees) > 0:
                                print(f"      ❌ ISSUE: {len(inactive_employees)} employees are not Active")
                                # Show examples of non-active statuses
                                for k, row in enumerate(inactive_employees[:3]):
                                    print(f"         Example: {row[name_idx] if name_idx else 'Unknown'} - Status: {row[status_idx]}")
                            else:
                                print(f"      ⚠️  No active employees found")
                        else:
                            print(f"   ❌ Status column not found in headers: {headers}")
                        
                        if len(rows) < 85:  # Total employees
                            print(f"   ✅ FILTERING APPLIED! ({len(rows)} < 85 total employees)")
                        else:
                            print(f"   ❌ NO FILTERING: Returned all {len(rows)} employees")
                    
                else:
                    print(f"   ❌ Query Failed: {data.get('error')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Status Filtering Test Complete!")

if __name__ == "__main__":
    test_status_filtering()
