"""
API Client Example for Report Manager Orchestrator

This script demonstrates how to interact with the orchestrator API.
"""

import requests
import json
from typing import Dict, Any


class OrchestratorAPIClient:
    """Client for interacting with the Report Manager Orchestrator API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        Initialize the API client
        
        Args:
            base_url: Base URL of the API server
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def query(self, text: str, complex_flow: bool = False, output_format: str = "json") -> Dict[str, Any]:
        """
        Send a query to the orchestrator
        
        Args:
            text: Query text
            complex_flow: Whether to use complex multi-step flow
            output_format: Output format (json, table, text)
            
        Returns:
            API response as dictionary
        """
        url = f"{self.base_url}/query"
        payload = {
            "text": text,
            "complex_flow": complex_flow,
            "output_format": output_format
        }
        
        response = self.session.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    
    def get_status(self) -> Dict[str, Any]:
        """Get system status"""
        url = f"{self.base_url}/status"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def list_agents(self) -> Dict[str, Any]:
        """List all available agents"""
        url = f"{self.base_url}/agents"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def get_agent_info(self, agent_name: str) -> Dict[str, Any]:
        """Get information about a specific agent"""
        url = f"{self.base_url}/agents/{agent_name}"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def get_flow_status(self, flow_id: str) -> Dict[str, Any]:
        """Get status of a specific flow"""
        url = f"{self.base_url}/flows/{flow_id}"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def list_flows(self) -> Dict[str, Any]:
        """List all active flows"""
        url = f"{self.base_url}/flows"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def test_connection(self) -> Dict[str, Any]:
        """Test LLM connection"""
        url = f"{self.base_url}/test-connection"
        response = self.session.post(url)
        response.raise_for_status()
        return response.json()
    
    def health_check(self) -> Dict[str, Any]:
        """Check API health"""
        url = f"{self.base_url}/health"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()


def main():
    """Example usage of the API client"""
    print("🚀 Report Manager Orchestrator API Client Example")
    print("=" * 60)
    
    # Initialize client
    client = OrchestratorAPIClient()
    
    try:
        # Health check
        print("\n🏥 Health Check:")
        health = client.health_check()
        print(f"   Status: {health['status']}")
        print(f"   System Initialized: {health['system_initialized']}")
        
        # Get system status
        print("\n📊 System Status:")
        status = client.get_status()
        print(f"   System: {status['system']}")
        print(f"   Available Agents: {status['available_agents']}")
        print(f"   Active Flows: {status['active_flows']}")
        
        # List agents
        print("\n🤖 Available Agents:")
        agents = client.list_agents()
        for agent in agents['agents']:
            print(f"   - {agent['name']}: {agent['info'].get('capabilities', [])}")
        
        # Test connection
        print("\n🔗 Testing LLM Connection:")
        connection_test = client.test_connection()
        print(f"   Success: {connection_test['success']}")
        if connection_test['success']:
            print(f"   Confidence: {connection_test['confidence']}")
            print(f"   Intent: {connection_test['intent']}")
        
        # Example queries
        example_queries = [
            "Show me all reports",
            "Find completed sales reports",
            "Analyze customer metrics trends",
            "Count reports by status"
        ]
        
        print("\n📝 Example Queries:")
        for i, query_text in enumerate(example_queries, 1):
            print(f"\n   Query {i}: {query_text}")
            try:
                result = client.query(query_text)
                print(f"      Success: {result['success']}")
                print(f"      Intent: {result['context']['intent']}")
                print(f"      Task Type: {result['context']['task_type']}")
                print(f"      Agents Used: {result['agents_used']}")
                print(f"      Execution Time: {result['execution_time']:.3f}s")
                
                if result['result'] and isinstance(result['result'], dict):
                    print(f"      Result Keys: {list(result['result'].keys())}")
                
            except requests.exceptions.RequestException as e:
                print(f"      Error: {e}")
        
        # Example complex flow
        print("\n🌊 Complex Flow Example:")
        try:
            complex_result = client.query(
                "Generate a comprehensive analysis report of all sales data",
                complex_flow=True
            )
            print(f"   Success: {complex_result['success']}")
            print(f"   Flow ID: {complex_result['flow_id']}")
            print(f"   Agents Used: {complex_result['agents_used']}")
            
            if complex_result['flow_id']:
                # Check flow status
                flow_status = client.get_flow_status(complex_result['flow_id'])
                print(f"   Flow Status: {flow_status['current_step']}")
                print(f"   Progress: {flow_status['completed_steps']}/{flow_status['total_steps']}")
                
        except requests.exceptions.RequestException as e:
            print(f"   Error: {e}")
        
        print("\n✅ API client example completed successfully!")
        
    except requests.exceptions.ConnectionError:
        print("\n❌ Could not connect to API server.")
        print("   Make sure the server is running:")
        print("   python api_server.py")
        print("   or")
        print("   python main.py serve")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    main()
