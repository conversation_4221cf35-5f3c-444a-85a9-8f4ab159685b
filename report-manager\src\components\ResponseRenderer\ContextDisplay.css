.context-display {
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.context-header {
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  transition: background-color 0.2s ease;
}

.context-header:hover {
  background: #f3f4f6;
}

.context-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-icon {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.context-content {
  padding: 0 16px 16px 16px;
  border-top: 1px solid #e5e7eb;
}

.context-item {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.context-item:last-child {
  margin-bottom: 0;
}

.context-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.context-value {
  font-size: 14px;
  color: #1f2937;
}

.confidence {
  font-weight: 600;
}

.confidence-high {
  color: #059669;
}

.confidence-medium {
  color: #d97706;
}

.confidence-low {
  color: #dc2626;
}

.entities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.entity-tag {
  background: #e0f2fe;
  color: #0369a1;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.parameters-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.parameter-item {
  display: flex;
  gap: 8px;
  font-size: 13px;
}

.param-key {
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
}

.param-value {
  color: #1f2937;
  font-family: 'Courier New', monospace;
}
