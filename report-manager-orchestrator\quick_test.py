#!/usr/bin/env python3
import requests

response = requests.post(
    "http://localhost:8009/query/excel-clean",
    json={"text": "give me employees whose first name start with letter R"},
    timeout=30
)

if response.status_code == 200:
    data = response.json()
    if data.get('success'):
        sheet = data['sheets'][0]
        metadata = data['metadata']
        
        print(f"Rows returned: {len(sheet.get('rows', []))}")
        print(f"Generated SQL: {metadata.get('sql_query')}")
        
        # Show first few names
        rows = sheet.get('rows', [])
        headers = sheet.get('headers', [])
        name_idx = headers.index('FullName') if 'FullName' in headers else 0
        
        print("First 5 names:")
        for i, row in enumerate(rows[:5]):
            print(f"  {i+1}. {row[name_idx]}")
    else:
        print(f"Error: {data.get('error')}")
else:
    print(f"HTTP Error: {response.status_code}")
