import { useState, useRef, useEffect } from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import streamingApi from '../services/streamingApi';
import ResponseRenderer from './ResponseRenderer/ResponseRenderer';
import DownloadRenderer from './DownloadRenderer/DownloadRenderer';
import ExcelGenerator from '../utils/excelGenerator';
import '../assets/Style/ChatWindow.css';

export default function ChatWindow({ conversation, onSendMessage }) {
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  // Debug logging
  console.log('ChatWindow rendered with conversation:', conversation);
  console.log('Conversation messages count:', conversation?.messages?.length || 0);
  
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation?.messages]);

  useEffect(() => {
    if (transcript) {
      setMessage(transcript);
    }
  }, [transcript]);

  const handleSend = async () => {
    if (message.trim() && !isLoading) {
      const userMessage = message.trim();
      // Don't clear message immediately - wait for successful send
      resetTranscript();

      try {
        setIsLoading(true);

        // Add user message to chat immediately with proper structure
        const userMessageObj = {
          text: userMessage,
          timestamp: new Date(),
          success: true,
          isStructured: false,
          originalQuery: userMessage
        };

        console.log('Adding user message:', userMessageObj);
        onSendMessage(userMessageObj, 'user');

        // Clear message only after successfully adding to conversation
        setMessage('');

        // Call the streaming API via service
        console.log('Calling streaming API with:', userMessage);

        // Add initial processing message
        const processingMessage = {
          text: '🔄 Processing your request...',
          timestamp: new Date(),
          success: true,
          isStructured: false,
          isProcessing: true,
          originalQuery: userMessage
        };
        onSendMessage(processingMessage, 'assistant');

        const result = await streamingApi.generateReport(userMessage, 'excel', (progressData) => {
          console.log('Progress update:', progressData);

          // Update processing message with progress
          let progressText = 'Processing...';

          if (progressData.type === 'data') {
            progressText = `Processing data chunks...`;
          } else if (progressData.message) {
            progressText = progressData.message;
          } else if (progressData.status) {
            progressText = progressData.status;
          }

          const percentage = progressData.percentage || 0;

          const updatedMessage = {
            text: `🔄 ${progressText}${percentage > 0 ? ` (${percentage}%)` : ''}`,
            timestamp: new Date(),
            success: true,
            isStructured: false,
            isProcessing: true,
            originalQuery: userMessage
          };

          // For now, just log progress. In a full implementation,
          // you'd update the existing message in the conversation
          console.log('Progress message:', updatedMessage);
        });
        console.log('Streaming API result:', result);
        console.log('Result type:', typeof result);
        console.log('Result keys:', Object.keys(result || {}));
        console.log('Result.success:', result?.success);
        console.log('Result.excelData:', result?.excelData);
        console.log('Result.chunks:', result?.chunks);
        console.log('Result.type:', result?.type);

        if (result.success && result.downloadInfo) {
          // Handle successful report generation with download info
          console.log('Processing successful report generation:', result);

          try {
            const responseMessage = {
              text: result.message || 'Report generated successfully!',
              timestamp: new Date(),
              success: true,
              isStructured: false,
              downloadInfo: result.downloadInfo,
              metadata: result.metadata,
              originalQuery: userMessage
            };

            onSendMessage(responseMessage, 'assistant');
            console.log('Download info sent to UI:', result.downloadInfo);

          } catch (error) {
            console.error('Error processing download:', error);
            const errorMessage = {
              text: `📊 Report generated but download preparation failed: ${error.message}`,
              timestamp: new Date(),
              success: false,
              isStructured: false,
              originalQuery: userMessage
            };
            onSendMessage(errorMessage, 'assistant');
          }
        } else {
          // Handle unexpected response - show debug info
          console.log('Unexpected response format:', result);
          const errorMessage = {
            text: `⚠️ Unexpected response format.\n\nReceived: ${JSON.stringify(result, null, 2)}\n\nPlease check console for details.`,
            timestamp: new Date(),
            success: false,
            isStructured: false,
            originalQuery: userMessage
          };
          onSendMessage(errorMessage, 'assistant');
        }

      } catch (error) {
        console.error('API Error:', error);
        const errorMessage = {
          text: 'Sorry, I encountered an error processing your request. Please try again.',
          timestamp: new Date(),
          success: false
        };
        onSendMessage(errorMessage, 'assistant');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Test function to create a mock Excel download with exact backend format
  const addTestDownload = () => {
    try {
      console.log('Creating test download with exact backend format...');

      // Create test data matching exact backend response format
      const testStreamingData = {
        sheets: {
          "Sales Data": {
            headers: ["product", "sales", "revenue", "month"],
            rows: [
              ["Product A", 1200, 24000, "January"],
              ["Product B", 850, 17000, "January"],
              ["Product C", 950, 19000, "January"],
              ["Product A", 1350, 27000, "February"],
              ["Product B", 920, 18400, "February"],
              ["Product C", 1100, 22000, "February"]
            ]
          }
        },
        metadata: {
          title: "Test Sales Data Report",
          generated_at: new Date().toISOString()
        }
      };

      console.log('Test streaming data:', testStreamingData);

      // Generate Excel file using the same method as real data
      const filename = `Report__Test_Sales_Data__${Date.now()}.xlsx`;
      const excelInfo = ExcelGenerator.generateFromApiResponse(
        { streamingData: testStreamingData },
        filename
      );

      console.log('Test Excel generation result:', excelInfo);

      const downloadMessage = {
        text: `📊 Report generated successfully!\n\n📋 **Report: Test Sales Data**\n\nFile: ${excelInfo.filename}\nSize: ${(excelInfo.size / 1024).toFixed(1)} KB`,
        timestamp: new Date(),
        success: true,
        isStructured: false,
        originalQuery: "Test Sales Data",
        downloadInfo: excelInfo
      };

      console.log('Sending test download message:', downloadMessage);
      onSendMessage("Generate test Excel download", 'user');
      onSendMessage(downloadMessage, 'assistant');
    } catch (error) {
      console.error('Test Excel generation failed:', error);
      onSendMessage("Generate test Excel download", 'user');
      onSendMessage({
        text: `❌ Test Excel generation failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test function to add sample structured data
  const addTestData = () => {
    const testData = {
      "success": true,
      "result": {
        "success": true,
        "data": {
          "reports": [
            {"id": 1, "title": "Q1 Sales Report", "type": "sales", "date": "2024-01-15", "status": "completed"},
            {"id": 2, "title": "Marketing Analysis", "type": "marketing", "date": "2024-02-01", "status": "draft"}
          ],
          "metrics": [
            {"name": "revenue", "value": 150000, "period": "Q1", "trend": "up"},
            {"name": "customers", "value": 1250, "period": "Q1", "trend": "up"}
          ]
        },
        "query_type": "data_retrieval",
        "metadata": {"entities_found": ["sales data"], "data_sources_accessed": ["reports", "metrics"]},
        "error": null
      },
      "context": {"intent": "view_sales_data", "task_type": "query", "entities": ["sales data"], "confidence": 0.9, "parameters": {}},
      "agents_used": ["query_agent"],
      "execution_time": 4.6275827999998,
      "flow_id": null,
      "error": null
    };

    const assistantMessage = {
      text: testData,
      agents_used: ["query_agent"],
      timestamp: new Date(),
      success: true,
      isStructured: true,
      originalQuery: "test structured data"
    };

    onSendMessage("Show me test structured data", 'user');
    onSendMessage(assistantMessage, 'assistant');
  };

  // Test API connection directly
  const testApiDirect = async () => {
    try {
      console.log('Testing direct API call...');
      const response = await fetch('http://localhost:8000/query/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'test query for debugging',
          output_format: 'json'
        })
      });

      console.log('Direct API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      const text = await response.text();
      console.log('Raw response text:', text);

      try {
        const json = JSON.parse(text);
        console.log('Parsed JSON:', json);

        onSendMessage("Direct API test", 'user');
        onSendMessage({
          text: `✅ Direct API test successful!\n\nResponse: ${JSON.stringify(json, null, 2)}`,
          timestamp: new Date(),
          success: true,
          isStructured: false
        }, 'assistant');
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        onSendMessage("Direct API test", 'user');
        onSendMessage({
          text: `❌ API returned non-JSON response:\n\n${text}`,
          timestamp: new Date(),
          success: false,
          isStructured: false
        }, 'assistant');
      }
    } catch (error) {
      console.error('Direct API test failed:', error);
      onSendMessage("Direct API test", 'user');
      onSendMessage({
        text: `❌ API test failed: ${error.message}`,
        timestamp: new Date(),
        success: false,
        isStructured: false
      }, 'assistant');
    }
  };

  // Generate Excel from last API response
  const generateExcelFromLastResponse = () => {
    try {
      // Find the last assistant message with structured data
      const lastStructuredMessage = conversation.messages
        .filter(msg => msg.type === 'assistant' && (msg.isStructured || msg.text))
        .pop();

      if (!lastStructuredMessage) {
        onSendMessage("No data available for Excel generation", 'user');
        onSendMessage({
          text: '❌ No structured data found in recent messages to generate Excel from.',
          timestamp: new Date(),
          success: false
        }, 'assistant');
        return;
      }

      // Generate Excel from the message data
      const responseData = typeof lastStructuredMessage.text === 'object'
        ? lastStructuredMessage.text
        : { message: lastStructuredMessage.text, agents_used: lastStructuredMessage.agents_used };

      const excelInfo = ExcelGenerator.generateFromApiResponse(
        responseData,
        `manual_report_${Date.now()}.xlsx`
      );

      const downloadMessage = {
        text: `📊 Excel generated from last response!\n\nFile: ${excelInfo.filename}\nSize: ${(excelInfo.size / 1024).toFixed(1)} KB`,
        timestamp: new Date(),
        success: true,
        isStructured: false,
        downloadInfo: excelInfo
      };

      onSendMessage("Generate Excel from last response", 'user');
      onSendMessage(downloadMessage, 'assistant');

    } catch (error) {
      console.error('Manual Excel generation failed:', error);
      onSendMessage("Generate Excel from last response", 'user');
      onSendMessage({
        text: `❌ Excel generation failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test Excel generation specifically
  const testExcelGeneration = async () => {
    try {
      console.log('Testing Excel generation...');
      const response = await fetch('http://localhost:8000/query/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'generate excel report with sample data',
          output_format: 'excel'
        })
      });

      console.log('Excel API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      const contentType = response.headers.get('content-type');
      const contentDisposition = response.headers.get('content-disposition');

      console.log('Content analysis:', {
        contentType,
        contentDisposition,
        isExcel: contentType && contentType.includes('excel'),
        isAttachment: contentDisposition && contentDisposition.includes('attachment')
      });

      if (contentType && (contentType.includes('excel') || contentType.includes('spreadsheet'))) {
        const blob = await response.blob();
        const downloadUrl = URL.createObjectURL(blob);

        onSendMessage("Test Excel generation", 'user');
        onSendMessage({
          text: `✅ Excel file detected!\n\nSize: ${blob.size} bytes\nType: ${blob.type}`,
          timestamp: new Date(),
          success: true,
          downloadInfo: {
            downloadUrl,
            filename: 'test-report.xlsx',
            size: blob.size,
            format: 'excel'
          }
        }, 'assistant');
      } else {
        const text = await response.text();
        onSendMessage("Test Excel generation", 'user');
        onSendMessage({
          text: `❌ Expected Excel file but got:\n\nContent-Type: ${contentType}\nResponse: ${text.substring(0, 500)}...`,
          timestamp: new Date(),
          success: false
        }, 'assistant');
      }
    } catch (error) {
      console.error('Excel test failed:', error);
      onSendMessage("Test Excel generation", 'user');
      onSendMessage({
        text: `❌ Excel test failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  const startListening = () => {
    setIsListening(true);
    resetTranscript();
    SpeechRecognition.startListening({ continuous: true });
  };

  const stopListening = () => {
    setIsListening(false);
    SpeechRecognition.stopListening();
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className="chat-interface">
        <div className="error-message">
          Browser doesn't support speech recognition. Please use Chrome or Edge.
        </div>
      </div>
    );
  }

  const hasMessages = conversation?.messages?.length > 0;

  return (
    <div className="chat-interface">
      {!hasMessages ? (
        <div className="welcome-view">
          <div className="welcome-container">
            <div className="brand-title">Good to see you, User.</div>

            {/* Large centered input box for welcome screen */}
            <div className="welcome-input-container">
              <div className="welcome-input-wrapper-large">
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Ask anything"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                  className="chat-input-large"
                  rows="1"
                />

                {/* Tools positioned below the input area */}
                <div className="welcome-tools-bottom">
                  <div className="left-tools">
                    <button className="tool-btn" title="Add">
                      +
                    </button>
                    <button className="tool-btn" title="Tools">
                      <span className="tool-icon">⚙</span>
                      <span>Tools</span>
                    </button>
                    <button className="tool-btn" onClick={addTestDownload} title="Test Download">
                      🧪 Test
                    </button>
                  </div>
                  <div className="right-tools">
                    <button
                      className={`tool-btn voice-btn ${isListening ? 'listening' : ''}`}
                      onClick={isListening ? stopListening : startListening}
                      title={isListening ? 'Stop listening' : 'Start voice input'}
                    >
                      🎤
                    </button>
                    <button
                      className="tool-btn send-btn"
                      onClick={handleSend}
                      disabled={!message.trim()}
                      title="Send message"
                    >
                      ↗
                    </button>
                  </div>
                </div>
              </div>

              {/* Welcome screen disclaimer */}
              <div className="welcome-disclaimer">
                <p>ReportDesk can make mistakes. Check important info.</p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="messages-area">
          {console.log('Rendering conversation:', conversation)}
          {console.log('Total messages:', conversation?.messages?.length || 0)}

          {/* Test message to verify rendering */}
          {(!conversation?.messages || conversation.messages.length === 0) && (
            <div className="message assistant">
              <div className="assistant-message">
                <div className="assistant-content">
                  <div className="assistant-text">
                    👋 Hello! I'm ready to help you generate reports. Try asking me something like "I need all tax codes from einvoicing"
                  </div>
                </div>
              </div>
            </div>
          )}

          {(conversation?.messages || []).map((msg, index) => {
            console.log(`Message ${index}:`, msg);
            console.log(`Message type: ${msg.type}, text: ${msg.text?.substring(0, 50)}...`);
            console.log(`Has downloadInfo:`, !!msg.downloadInfo);
            console.log(`downloadInfo value:`, msg.downloadInfo);
            console.log(`downloadInfo type:`, typeof msg.downloadInfo);
            if (msg.downloadInfo) {
              console.log(`downloadInfo keys:`, Object.keys(msg.downloadInfo));
              console.log(`downloadInfo.downloadUrl:`, msg.downloadInfo.downloadUrl);
            }
            return (
            <div key={index} className={`message ${msg.type}`}>
              {msg.type === 'user' ? (
                // User message display
                <div className="user-message">
                  <div className="user-content">
                    <div className="user-text">{msg.text}</div>
                  </div>
                </div>
              ) : (
                // Assistant message display
                <div className="assistant-message">
                  <div className="assistant-content">
                    {msg.isStructured ? (
                      <ResponseRenderer
                        response={msg.text}
                        userMessage={msg.originalQuery || ''}
                      />
                    ) : (msg.downloadInfo && msg.downloadInfo.downloadUrl) ? (
                      <div>
                        <div className="assistant-text">
                          {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                        </div>
                        {console.log('Rendering download info:', msg.downloadInfo)}
                        <DownloadRenderer
                          downloadInfo={msg.downloadInfo}
                          onDownload={(info) => console.log('Downloaded:', info)}
                        />
                      </div>
                    ) : (
                      <div className="assistant-text">
                        {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            );
          })}
          {isLoading && (
            <div className="typing-indicator">
              <div className="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span className="typing-text">Processing your request...</span>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Input area only visible when there are messages */}
      {hasMessages && (
        <div className="input-area">
          <div className="input-container">
            <div className="input-wrapper">
              <div className="left-actions">
                <button className="add-btn" title="Add">
                  +
                </button>
                <button className="tools-btn" title="Tools">
                  <span className="tools-icon">⚙</span>
                  <span>Tools</span>
                </button>
              </div>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Ask anything"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSend();
                  }
                }}
                className="chat-input"
                rows="1"
              />
              <div className="right-actions">
                <button
                  className={`action-btn voice-btn ${isListening ? 'listening' : ''}`}
                  onClick={isListening ? stopListening : startListening}
                  title={isListening ? 'Stop listening' : 'Start voice input'}
                >
                  🎤
                </button>
                <button
                  className="action-btn send-btn"
                  onClick={handleSend}
                  disabled={!message.trim()}
                  title="Send message"
                >
                  ↗
                </button>
              </div>
            </div>
          </div>

          {/* ChatGPT-style disclaimer */}
          <div className="disclaimer">
            <p>ReportDesk can make mistakes. Check important info.</p>
          </div>
        </div>
      )}
    </div>
  );
}

