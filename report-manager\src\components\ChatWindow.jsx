import { useState, useRef, useEffect } from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import streamingApi from '../services/streamingApi';
import ResponseRenderer from './ResponseRenderer/ResponseRenderer';
import DownloadRenderer from './DownloadRenderer/DownloadRenderer';
import '../assets/Style/ChatWindow.css';

export default function ChatWindow({ conversation, onSendMessage }) {
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);

  // Debug logging
  console.log('ChatWindow rendered with conversation:', conversation);
  console.log('Conversation messages count:', conversation?.messages?.length || 0);
  
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation?.messages]);

  useEffect(() => {
    if (transcript) {
      setMessage(transcript);
    }
  }, [transcript]);

  // Auto-resize textarea
  const handleTextareaChange = (e) => {
    setMessage(e.target.value);
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  };

  const handleSend = async () => {
    if (message.trim() && !isLoading) {
      const userMessage = message.trim();
      resetTranscript();

      try {
        setIsLoading(true);

        // Add user message to chat immediately
        const userMessageObj = {
          id: 'user_' + Date.now(),
          type: 'user',
          text: userMessage,
          timestamp: new Date(),
          success: true,
          isStructured: false,
          originalQuery: userMessage
        };

        onSendMessage(userMessageObj, 'user');
        setMessage('');

        // Wait a moment to ensure user message is rendered
        await new Promise(resolve => setTimeout(resolve, 100));

        // TODO: Add processing message back later if needed
        // Call the streaming API directly
        const result = await streamingApi.generateReport(userMessage, 'excel', (progressData) => {
          console.log('Progress update:', progressData);
        });

        if (result.success && result.downloadInfo) {
          // Create success response message
          const responseMessage = {
            id: 'response_' + Date.now(),
            type: 'assistant',
            text: result.message || 'Report generated successfully!',
            timestamp: new Date(),
            success: true,
            isStructured: false,
            downloadInfo: result.downloadInfo,
            metadata: result.metadata,
            originalQuery: userMessage
          };
          onSendMessage(responseMessage, 'assistant');
        } else {
          // Create error response message
          const errorMessage = {
            id: 'error_' + Date.now(),
            type: 'assistant',
            text: 'Sorry, I encountered an issue generating your report. Please try again.',
            timestamp: new Date(),
            success: false,
            isStructured: false,
            originalQuery: userMessage
          };
          onSendMessage(errorMessage, 'assistant');
        }

      } catch (error) {
        console.error('Error in handleSend:', error);
        const errorMessage = {
          id: 'error_' + Date.now(),
          type: 'assistant',
          text: 'Sorry, I encountered an error processing your request. Please try again.',
          timestamp: new Date(),
          success: false,
          isStructured: false
        };
        onSendMessage(errorMessage, 'assistant');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const startListening = () => {
    setIsListening(true);
    resetTranscript();
    SpeechRecognition.startListening({ continuous: true });
  };

  const stopListening = () => {
    setIsListening(false);
    SpeechRecognition.stopListening();
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className="chat-interface">
        <div className="no-speech-support">
          <p>Browser doesn't support speech recognition.</p>
        </div>
      </div>
    );
  }

  const hasMessages = conversation?.messages?.length > 0;

  return (
    <div className="chat-interface">
      {!hasMessages ? (
        // Welcome View - Center-aligned message box
        <div className="welcome-view">
          <div className="welcome-container">
            <h1 className="brand-title">Report Manager</h1>
            <div className="welcome-input-container">
              <div className="welcome-input-wrapper-large">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={handleTextareaChange}
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSend()}
                  placeholder={isLoading ? 'Processing your request...' : (isListening ? 'Listening...' : 'Ask me to generate a report...')}
                  disabled={isLoading}
                  className="chat-input-large"
                  rows={1}
                />
                <div className="welcome-tools-bottom">
                  <div className="left-tools">
                    <button
                      className={`tool-btn ${isListening ? 'listening' : ''}`}
                      onClick={isListening ? stopListening : startListening}
                      title={isListening ? 'Stop listening' : 'Start voice input'}
                    >
                      {isListening ? '🔴' : '🎤'}
                    </button>
                  </div>
                  <div className="right-tools">
                    <button
                      onClick={handleSend}
                      disabled={!message.trim() || isLoading}
                      className="send-btn"
                    >
                      {isLoading ? '⏳' : '➤'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Messages View - Regular chat interface
        <>
          <div className="messages-area">
            {(conversation?.messages || []).map((msg, index) => {
              console.log(`Rendering Message ${index}:`, {
                type: msg.type,
                text: msg.text?.substring(0, 50) + '...',
                hasDownloadInfo: !!msg.downloadInfo,
                timestamp: msg.timestamp
              });

              return (
              <div key={index} className={`message ${msg.type}`}>
                {msg.type === 'user' ? (
                  // User message display
                  <div className="user-message">
                    <div className="user-content">
                      <div className="user-text">{msg.text}</div>
                    </div>
                  </div>
                ) : (
                  // Assistant message display
                  <div className="assistant-message">
                    <div className="assistant-content">
                      {msg.isStructured ? (
                        <ResponseRenderer
                          response={msg.text}
                          userMessage={msg.originalQuery || ''}
                        />
                      ) : (msg.downloadInfo && msg.downloadInfo.downloadUrl) ? (
                        <div>
                          <div className="assistant-text">
                            {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                          </div>
                          <DownloadRenderer
                            downloadInfo={msg.downloadInfo}
                            onDownload={(info) => console.log('Downloaded:', info)}
                          />
                        </div>
                      ) : (
                        <div className="assistant-text">
                          {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="input-container">
            <div className="input-wrapper">
              <div className="left-actions">
                <button
                  className={`mic-btn ${isListening ? 'listening' : ''}`}
                  onClick={isListening ? stopListening : startListening}
                  title={isListening ? 'Stop listening' : 'Start voice input'}
                >
                  {isListening ? '🔴' : '🎤'}
                </button>
              </div>

              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSend()}
                placeholder={isLoading ? 'Processing your request...' : (isListening ? 'Listening...' : 'Ask me to generate a report...')}
                disabled={isLoading}
                className="message-input"
              />

              <button
                onClick={handleSend}
                disabled={!message.trim() || isLoading}
                className="send-btn"
              >
                {isLoading ? '⏳' : '➤'}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
