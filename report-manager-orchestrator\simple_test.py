#!/usr/bin/env python3
"""
Simple test for the Excel endpoint
"""

import requests
import json

def test_excel_endpoint():
    """Test the Excel endpoint"""
    try:
        url = "http://localhost:8000/query/excel"
        data = {"text": "I need all einvoicing Tax code"}
        
        print("🧪 Testing Excel Endpoint")
        print(f"URL: {url}")
        print(f"Data: {data}")
        
        response = requests.post(url, json=data, timeout=5)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Response: {json.dumps(result, indent=2)}")
            
            # Check if it's tax code data
            if 'sheets' in result:
                sheets = result['sheets']
                print(f"\n📊 Analysis:")
                print(f"Number of sheets: {len(sheets)}")
                
                for sheet in sheets:
                    name = sheet.get('name', 'Unknown')
                    headers = sheet.get('headers', [])
                    print(f"Sheet: {name}")
                    print(f"Headers: {headers}")
                    
                    # Check if it's tax code data
                    if 'tax' in name.lower():
                        print("✅ Contains tax code data")
                    else:
                        print("❌ Does not contain tax code data")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Server not running")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_excel_endpoint()
