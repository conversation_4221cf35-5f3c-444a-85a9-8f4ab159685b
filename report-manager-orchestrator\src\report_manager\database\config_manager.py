"""
Database Configuration Manager

This module handles loading and managing database configurations,
including connection strings, table metadata, and routing rules.
"""

import os
import yaml
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path
from loguru import logger


@dataclass
class TableColumn:
    """Represents a database table column with metadata"""
    name: str
    type: str
    description: str


@dataclass
class TableInfo:
    """Represents a database table with its metadata"""
    name: str
    description: str
    columns: Dict[str, TableColumn]
    keywords: List[str]
    common_queries: List[str]


@dataclass
class DataSource:
    """Represents a database data source configuration"""
    name: str
    description: str
    type: str
    connection: Dict[str, Any]
    tables: Dict[str, TableInfo]


@dataclass
class RoutingRule:
    """Represents a query routing rule"""
    keywords: List[str]
    primary_datasource: str
    tables: List[str]


class DatabaseConfigManager:
    """
    Manages database configuration including data sources,
    routing rules, and security settings.
    """
    
    def __init__(self, config_path: str = "config/database_config.yaml"):
        """
        Initialize the configuration manager
        
        Args:
            config_path: Path to the database configuration file
        """
        self.config_path = config_path
        self.config = None
        self.data_sources: Dict[str, DataSource] = {}
        self.routing_rules: Dict[str, RoutingRule] = {}
        self.security_settings = {}
        self.access_control = {}
        self.query_templates = {}
        
        self.load_config()
    
    def load_config(self):
        """Load configuration from YAML file"""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                logger.warning(f"Config file not found: {self.config_path}")
                self._create_default_config()
                return
            
            with open(config_file, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
            
            self._parse_config()
            logger.info(f"Database configuration loaded from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error loading database config: {e}")
            self._create_default_config()
    
    def _parse_config(self):
        """Parse the loaded configuration"""
        if not self.config:
            return
        
        # Parse data sources
        self._parse_data_sources()
        
        # Parse routing rules
        self._parse_routing_rules()
        
        # Parse security settings
        self.security_settings = self.config.get('security', {})
        
        # Parse access control
        self.access_control = self.config.get('access_control', {})
        
        # Parse query templates
        self.query_templates = self.config.get('query_templates', {})
    
    def _parse_data_sources(self):
        """Parse data source configurations"""
        data_sources_config = self.config.get('data_sources', {})
        
        for ds_key, ds_config in data_sources_config.items():
            # Parse tables
            tables = {}
            tables_config = ds_config.get('tables', {})
            
            for table_key, table_config in tables_config.items():
                # Parse columns
                columns = {}
                columns_config = table_config.get('columns', {})
                
                for col_name, col_config in columns_config.items():
                    columns[col_name] = TableColumn(
                        name=col_name,
                        type=col_config.get('type', 'string'),
                        description=col_config.get('description', '')
                    )
                
                tables[table_key] = TableInfo(
                    name=table_key,
                    description=table_config.get('description', ''),
                    columns=columns,
                    keywords=table_config.get('keywords', []),
                    common_queries=table_config.get('common_queries', [])
                )
            
            # Resolve environment variables in connection config
            connection_config = self._resolve_env_vars(ds_config.get('connection', {}))
            
            self.data_sources[ds_key] = DataSource(
                name=ds_config.get('name', ds_key),
                description=ds_config.get('description', ''),
                type=ds_config.get('type', 'postgresql'),
                connection=connection_config,
                tables=tables
            )
    
    def _parse_routing_rules(self):
        """Parse routing rules configuration"""
        routing_config = self.config.get('routing_rules', {})
        intents_config = routing_config.get('intents', {})
        
        for intent_key, intent_config in intents_config.items():
            self.routing_rules[intent_key] = RoutingRule(
                keywords=intent_config.get('keywords', []),
                primary_datasource=intent_config.get('primary_datasource', ''),
                tables=intent_config.get('tables', [])
            )
    
    def _resolve_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Resolve environment variables in configuration values
        
        Format: ${VAR_NAME:default_value}
        """
        resolved_config = {}
        
        for key, value in config.items():
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                # Extract variable name and default value
                var_expr = value[2:-1]  # Remove ${ and }
                
                if ':' in var_expr:
                    var_name, default_value = var_expr.split(':', 1)
                else:
                    var_name = var_expr
                    default_value = None
                
                # Get environment variable value
                resolved_value = os.getenv(var_name, default_value)
                
                if resolved_value is None:
                    logger.warning(f"Environment variable {var_name} not set and no default provided")
                    resolved_value = ""
                
                resolved_config[key] = resolved_value
            else:
                resolved_config[key] = value
        
        return resolved_config
    
    def _create_default_config(self):
        """Create a default configuration if none exists"""
        logger.info("Creating default database configuration")
        
        # Create a minimal default configuration
        self.data_sources = {
            'default_db': DataSource(
                name='Default Database',
                description='Default database configuration',
                type='sqlite',
                connection={'database': ':memory:'},
                tables={}
            )
        }
        
        self.security_settings = {
            'enable_sql_injection_protection': True,
            'allowed_operations': ['SELECT'],
            'max_query_length': 10000
        }
    
    def get_data_source(self, datasource_name: str) -> Optional[DataSource]:
        """Get a data source by name"""
        return self.data_sources.get(datasource_name)
    
    def get_all_data_sources(self) -> Dict[str, DataSource]:
        """Get all configured data sources"""
        return self.data_sources
    
    def find_relevant_datasources(self, query_text: str) -> List[str]:
        """
        Find relevant data sources based on query text
        
        Args:
            query_text: Natural language query
            
        Returns:
            List of relevant data source names
        """
        query_lower = query_text.lower()
        relevant_sources = []
        
        # Check routing rules
        for intent_name, rule in self.routing_rules.items():
            for keyword in rule.keywords:
                if keyword.lower() in query_lower:
                    if rule.primary_datasource not in relevant_sources:
                        relevant_sources.append(rule.primary_datasource)
                    break
        
        # If no matches found, check table keywords
        if not relevant_sources:
            for ds_name, datasource in self.data_sources.items():
                for table_name, table_info in datasource.tables.items():
                    for keyword in table_info.keywords:
                        if keyword.lower() in query_lower:
                            if ds_name not in relevant_sources:
                                relevant_sources.append(ds_name)
                            break
        
        # Fallback to default
        if not relevant_sources:
            fallback_config = self.config.get('routing_rules', {}).get('fallback', {})
            default_ds = fallback_config.get('default_datasource')
            if default_ds and default_ds in self.data_sources:
                relevant_sources.append(default_ds)
        
        return relevant_sources
    
    def find_relevant_tables(self, query_text: str, datasource_name: str) -> List[str]:
        """
        Find relevant tables in a data source based on query text
        
        Args:
            query_text: Natural language query
            datasource_name: Name of the data source
            
        Returns:
            List of relevant table names
        """
        query_lower = query_text.lower()
        relevant_tables = []
        
        datasource = self.get_data_source(datasource_name)
        if not datasource:
            return relevant_tables
        
        # Check table keywords
        for table_name, table_info in datasource.tables.items():
            for keyword in table_info.keywords:
                if keyword.lower() in query_lower:
                    if table_name not in relevant_tables:
                        relevant_tables.append(table_name)
                    break
        
        return relevant_tables
    
    def get_security_settings(self) -> Dict[str, Any]:
        """Get security settings"""
        return self.security_settings
    
    def get_access_control(self) -> Dict[str, Any]:
        """Get access control configuration"""
        return self.access_control
    
    def get_query_templates(self) -> Dict[str, Any]:
        """Get query templates"""
        return self.query_templates
    
    def validate_user_permissions(self, user_role: str, datasource: str, table: str) -> bool:
        """
        Validate if a user role has permission to access a specific table
        
        Args:
            user_role: User's role
            datasource: Data source name
            table: Table name
            
        Returns:
            True if access is allowed, False otherwise
        """
        roles = self.access_control.get('roles', {})
        role_config = roles.get(user_role)
        
        if not role_config:
            logger.warning(f"Unknown user role: {user_role}")
            return False
        
        permissions = role_config.get('permissions', [])
        
        # Check for exact match
        exact_permission = f"{datasource}.{table}"
        if exact_permission in permissions:
            return True
        
        # Check for wildcard match
        wildcard_permission = f"{datasource}.*"
        if wildcard_permission in permissions:
            return True
        
        return False
