#!/usr/bin/env python3
"""
Simple test server to verify SQLite database works
"""

import os
import asyncio
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from src.report_manager.database.query_engine import DatabaseQueryEngine

# Load environment variables
load_dotenv()

app = FastAPI(title="Simple Test Server")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global database query engine
db_query_engine = None

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    global db_query_engine
    
    print("🔍 Environment Check:")
    print(f"   INVOICING_DB_TYPE: {os.getenv('INVOICING_DB_TYPE')}")
    print(f"   INVOICING_DB_PATH: {os.getenv('INVOICING_DB_PATH')}")
    print(f"   Database exists: {os.path.exists(os.getenv('INVOICING_DB_PATH', 'data/invoicing.db'))}")
    
    # Create a mock LLM client
    class MockLLMClient:
        async def generate_response(self, prompt, **kwargs):
            return "Mock response"
    
    try:
        # Initialize database query engine
        print("🔧 Initializing database query engine...")
        db_query_engine = DatabaseQueryEngine(MockLLMClient())
        
        # Initialize the engine
        print("🔧 Initializing database connections...")
        await db_query_engine.initialize()
        
        print("✅ Database query engine ready!")
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        import traceback
        traceback.print_exc()

@app.get("/health")
async def health():
    """Health check"""
    return {
        "status": "healthy",
        "database_ready": db_query_engine is not None
    }

@app.post("/test-query")
async def test_query():
    """Test the tax code query"""
    if not db_query_engine:
        return {"success": False, "error": "Database not initialized"}
    
    try:
        # Create a mock session
        class MockSession:
            def __init__(self):
                self.user_id = "system"
                self.role = "admin"
        
        session = MockSession()
        
        # Test query
        query_text = "I need all tax code from einvoicing"
        result = await db_query_engine.execute_natural_language_query(query_text, session)
        
        if result.success:
            return {
                "success": True,
                "sql_query": result.sql_query,
                "datasource": result.datasource,
                "columns": result.columns,
                "row_count": result.row_count,
                "sample_data": result.data[:5] if result.data else []
            }
        else:
            return {
                "success": False,
                "error": result.error,
                "sql_query": result.sql_query
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting simple test server on port 8004...")
    uvicorn.run(app, host="0.0.0.0", port=8004, log_level="info")
