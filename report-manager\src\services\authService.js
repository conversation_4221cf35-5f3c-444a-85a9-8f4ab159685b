import { GOOGLE_AUTH_CONFIG, AUTH_ENDPOINTS, STORAGE_KEYS, DEMO_MODE, DEMO_USER } from '../config/auth.js';

class AuthService {
  constructor() {
    this.isInitialized = false;
    this.currentUser = null;
  }

  // Initialize Google Identity Services
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Check if we're in demo mode
      if (DEMO_MODE) {
        console.log('Running in demo mode - skipping Google Auth initialization');
        this.isInitialized = true;
        await this.checkExistingAuth();
        return;
      }

      // Load Google Identity Services script
      await this.loadGoogleScript();

      // Initialize Google Identity Services
      window.google.accounts.id.initialize({
        client_id: GOOGLE_AUTH_CONFIG.clientId,
        callback: this.handleCredentialResponse.bind(this),
        auto_select: false,
        cancel_on_tap_outside: true,
        ux_mode: 'popup',
        context: 'signin'
      });

      this.isInitialized = true;
      console.log('Google Auth initialized successfully');

      // Check if user is already logged in
      await this.checkExistingAuth();
    } catch (error) {
      console.error('Failed to initialize Google Auth:', error);
      throw error;
    }
  }

  // Load Google Identity Services script
  loadGoogleScript() {
    return new Promise((resolve, reject) => {
      if (window.google?.accounts?.id) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Handle credential response from Google
  async handleCredentialResponse(response) {
    try {
      console.log('Received credential response:', response);
      
      // Decode the JWT token to get user info
      const userInfo = this.parseJWT(response.credential);
      console.log('Parsed user info:', userInfo);

      // Store auth data
      this.storeAuthData({
        accessToken: response.credential,
        userInfo: userInfo,
        expiresAt: userInfo.exp * 1000 // Convert to milliseconds
      });

      this.currentUser = userInfo;
      
      // Trigger auth state change event
      this.triggerAuthStateChange(true, userInfo);
      
      return userInfo;
    } catch (error) {
      console.error('Error handling credential response:', error);
      throw error;
    }
  }

  // Parse JWT token
  parseJWT(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error parsing JWT:', error);
      throw error;
    }
  }

  // Sign in with Google
  async signIn() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Check if we're in demo mode
      if (DEMO_MODE) {
        console.log('Demo mode sign-in');
        return this.handleDemoSignIn();
      }

      // Use Google Identity Services with better error handling
      return new Promise((resolve, reject) => {
        // First try Google One Tap
        window.google.accounts.id.prompt((notification) => {
          console.log('Google prompt notification:', notification);

          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            console.log('One Tap not available, trying popup...');
            // Fallback to popup sign-in
            this.signInWithPopup().then(resolve).catch(reject);
          }
          // If One Tap is displayed, the callback will handle the response
        });

        // Set a timeout for One Tap
        setTimeout(() => {
          console.log('One Tap timeout, trying popup...');
          this.signInWithPopup().then(resolve).catch(reject);
        }, 3000);
      });
    } catch (error) {
      console.error('Sign-in error:', error);
      throw error;
    }
  }

  // Handle demo mode sign-in
  async handleDemoSignIn() {
    try {
      // Simulate a brief loading delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Store demo user data
      this.storeAuthData({
        accessToken: 'demo-token',
        userInfo: DEMO_USER,
        expiresAt: DEMO_USER.exp
      });

      this.currentUser = DEMO_USER;

      // Trigger auth state change event
      this.triggerAuthStateChange(true, DEMO_USER);

      return DEMO_USER;
    } catch (error) {
      console.error('Demo sign-in error:', error);
      throw error;
    }
  }

  // Sign in with popup (fallback method)
  async signInWithPopup() {
    try {
      // For demo mode, use demo sign in
      if (DEMO_MODE) {
        return this.handleDemoSignIn();
      }

      // Use Google Identity Services popup
      return new Promise((resolve, reject) => {
        if (!window.google?.accounts?.id) {
          reject(new Error('Google Identity Services not loaded'));
          return;
        }

        // Show Google One Tap
        window.google.accounts.id.prompt((notification) => {
          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            // If One Tap doesn't work, try direct OAuth
            this.directOAuthFlow().then(resolve).catch(reject);
          }
        });
      });
    } catch (error) {
      console.error('Popup sign-in error:', error);
      throw error;
    }
  }

  // Direct OAuth flow for better compatibility
  async directOAuthFlow() {
    try {
      // Create OAuth URL
      const authUrl = this.buildAuthUrl();

      // Open popup window
      const popup = window.open(
        authUrl,
        'google-auth',
        'width=500,height=600,scrollbars=yes,resizable=yes,left=' +
        (window.screen.width / 2 - 250) + ',top=' + (window.screen.height / 2 - 300)
      );

      if (!popup) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Listen for popup completion
      return new Promise((resolve, reject) => {
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            reject(new Error('Authentication cancelled by user'));
          }
        }, 1000);

        // Listen for URL changes in popup
        const checkAuth = setInterval(() => {
          try {
            const currentUrl = popup.location.href;
            console.log('Checking popup URL:', currentUrl);

            if (currentUrl.includes(window.location.origin)) {
              // User has been redirected back
              console.log('Popup redirected back to our domain');
              const url = new URL(currentUrl);

              // Check for implicit flow tokens (in hash)
              const hash = popup.location.hash;
              console.log('URL hash:', hash);

              if (hash) {
                const params = new URLSearchParams(hash.substring(1));
                const accessToken = params.get('access_token');
                const idToken = params.get('id_token');
                const error = params.get('error');

                console.log('Hash params:', { accessToken: !!accessToken, idToken: !!idToken, error });

                clearInterval(checkAuth);
                clearInterval(checkClosed);
                popup.close();

                if (error) {
                  reject(new Error(`OAuth error: ${error}`));
                } else if (accessToken && idToken) {
                  // Handle implicit flow tokens
                  console.log('Processing implicit flow tokens');
                  this.handleImplicitTokens(accessToken, idToken).then(resolve).catch(reject);
                  return;
                }
              }

              // Check for authorization code flow
              const code = url.searchParams.get('code');
              const error = url.searchParams.get('error');

              console.log('URL params:', { code: !!code, error });

              clearInterval(checkAuth);
              clearInterval(checkClosed);
              popup.close();

              if (error) {
                reject(new Error(`OAuth error: ${error}`));
              } else if (code) {
                console.log('Processing authorization code');
                this.handleAuthCode(code).then(resolve).catch(reject);
              } else {
                console.log('No tokens or code found in URL');
                reject(new Error('No authorization tokens or code received'));
              }
            }
          } catch (e) {
            // Cross-origin error, popup still on Google's domain
            console.log('Cross-origin error (expected while on Google domain)');
          }
        }, 1000);

        // Timeout after 5 minutes
        setTimeout(() => {
          clearInterval(checkAuth);
          clearInterval(checkClosed);
          if (!popup.closed) {
            popup.close();
          }
          reject(new Error('Authentication timeout'));
        }, 300000);
      });
    } catch (error) {
      console.error('Direct OAuth flow error:', error);
      throw error;
    }
  }

  // Build OAuth authorization URL
  buildAuthUrl() {
    const params = new URLSearchParams({
      client_id: GOOGLE_AUTH_CONFIG.clientId,
      redirect_uri: GOOGLE_AUTH_CONFIG.redirectUri,
      response_type: GOOGLE_AUTH_CONFIG.responseType,
      scope: GOOGLE_AUTH_CONFIG.scopes.join(' '),
      access_type: GOOGLE_AUTH_CONFIG.accessType,
      prompt: GOOGLE_AUTH_CONFIG.prompt,
      state: 'oauth_' + Date.now(), // Add state for security
      nonce: 'nonce_' + Date.now()  // Add nonce for security
    });

    console.log('OAuth URL params:', {
      client_id: GOOGLE_AUTH_CONFIG.clientId,
      redirect_uri: GOOGLE_AUTH_CONFIG.redirectUri,
      response_type: GOOGLE_AUTH_CONFIG.responseType
    });

    return `${AUTH_ENDPOINTS.googleAuth}?${params.toString()}`;
  }

  // Handle authorization code from OAuth redirect
  async handleAuthCode(code) {
    try {
      console.log('Handling auth code:', code);

      // In a real app, you'd send this code to your backend
      // For now, we'll simulate a successful auth
      const mockUser = {
        sub: 'oauth-user-' + Date.now(),
        name: 'OAuth User',
        email: '<EMAIL>',
        picture: 'https://via.placeholder.com/96x96/3b82f6/ffffff?text=OU',
        given_name: 'OAuth',
        family_name: 'User',
        exp: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now
      };

      // Store auth data
      this.storeAuthData({
        accessToken: 'oauth-token-' + code.substring(0, 10),
        userInfo: mockUser,
        expiresAt: mockUser.exp
      });

      this.currentUser = mockUser;

      // Trigger auth state change event
      this.triggerAuthStateChange(true, mockUser);

      return mockUser;
    } catch (error) {
      console.error('Error handling auth code:', error);
      throw error;
    }
  }

  // Handle implicit flow tokens
  async handleImplicitTokens(accessToken, idToken) {
    try {
      console.log('Handling implicit flow tokens');

      // Decode the ID token to get user info
      const userInfo = this.parseJWT(idToken);
      console.log('Parsed user info from ID token:', userInfo);

      // Store auth data
      this.storeAuthData({
        accessToken: accessToken,
        userInfo: userInfo,
        expiresAt: userInfo.exp * 1000 // Convert to milliseconds
      });

      this.currentUser = userInfo;

      // Trigger auth state change event
      this.triggerAuthStateChange(true, userInfo);

      return userInfo;
    } catch (error) {
      console.error('Error handling implicit tokens:', error);
      throw error;
    }
  }

  // Sign out
  async signOut() {
    try {
      console.log('AuthService: Starting sign out process...');

      // Sign out from Google
      if (window.google?.accounts?.id) {
        window.google.accounts.id.disableAutoSelect();
        console.log('AuthService: Disabled Google auto-select');
      }

      // Clear local storage
      this.clearAuthData();
      this.currentUser = null;
      console.log('AuthService: Cleared auth data and current user');

      // Trigger auth state change event
      this.triggerAuthStateChange(false, null);
      console.log('AuthService: Triggered auth state change');

      console.log('AuthService: User signed out successfully');
      return true;
    } catch (error) {
      console.error('AuthService: Sign-out error:', error);
      throw error;
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = localStorage.getItem(STORAGE_KEYS.accessToken);
    const expiresAt = localStorage.getItem(STORAGE_KEYS.expiresAt);
    
    if (!token || !expiresAt) return false;
    
    // Check if token is expired
    return Date.now() < parseInt(expiresAt);
  }

  // Get current user
  getCurrentUser() {
    if (this.currentUser) return this.currentUser;
    
    const userInfo = localStorage.getItem(STORAGE_KEYS.userInfo);
    if (userInfo) {
      this.currentUser = JSON.parse(userInfo);
      return this.currentUser;
    }
    
    return null;
  }

  // Check existing authentication
  async checkExistingAuth() {
    if (this.isAuthenticated()) {
      const userInfo = this.getCurrentUser();
      if (userInfo) {
        this.triggerAuthStateChange(true, userInfo);
        return userInfo;
      }
    }
    return null;
  }

  // Store authentication data
  storeAuthData({ accessToken, refreshToken, userInfo, expiresAt }) {
    localStorage.setItem(STORAGE_KEYS.accessToken, accessToken);
    if (refreshToken) {
      localStorage.setItem(STORAGE_KEYS.refreshToken, refreshToken);
    }
    localStorage.setItem(STORAGE_KEYS.userInfo, JSON.stringify(userInfo));
    localStorage.setItem(STORAGE_KEYS.expiresAt, expiresAt.toString());
  }

  // Clear authentication data
  clearAuthData() {
    localStorage.removeItem(STORAGE_KEYS.accessToken);
    localStorage.removeItem(STORAGE_KEYS.refreshToken);
    localStorage.removeItem(STORAGE_KEYS.userInfo);
    localStorage.removeItem(STORAGE_KEYS.expiresAt);
  }

  // Trigger auth state change event
  triggerAuthStateChange(isAuthenticated, user) {
    const event = new CustomEvent('authStateChange', {
      detail: { isAuthenticated, user }
    });
    window.dispatchEvent(event);
  }

  // Get access token
  getAccessToken() {
    return localStorage.getItem(STORAGE_KEYS.accessToken);
  }
}

// Create singleton instance
const authService = new AuthService();
export default authService;
