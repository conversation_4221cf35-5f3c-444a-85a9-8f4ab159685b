#!/usr/bin/env python3
"""
Test SQLite database connection directly
"""

import asyncio
import os
from dotenv import load_dotenv
from src.report_manager.database.query_engine import DatabaseQueryEngine

async def test_database():
    """Test the database connection and query execution"""
    
    # Load environment variables
    load_dotenv()
    
    print("🔍 Environment Check:")
    print(f"   INVOICING_DB_TYPE: {os.getenv('INVOICING_DB_TYPE')}")
    print(f"   INVOICING_DB_PATH: {os.getenv('INVOICING_DB_PATH')}")
    print(f"   Database exists: {os.path.exists(os.getenv('INVOICING_DB_PATH', 'data/invoicing.db'))}")
    
    # Create a mock LLM client
    class MockLLMClient:
        async def generate_response(self, prompt, **kwargs):
            return "Mock response"
    
    try:
        # Initialize database query engine
        print("\n🔧 Initializing database query engine...")
        db_query_engine = DatabaseQueryEngine(MockLLMClient())
        
        # Initialize the engine
        print("🔧 Initializing database connections...")
        await db_query_engine.initialize()
        
        # Create a mock session
        class MockSession:
            def __init__(self):
                self.user_id = "system"
                self.role = "admin"
        
        session = MockSession()
        
        # Test query
        print("\n🔍 Testing query execution...")
        query_text = "I need all tax code from einvoicing"
        result = await db_query_engine.execute_natural_language_query(query_text, session)
        
        print(f"\n📊 Query Results:")
        print(f"   Success: {result.success}")
        print(f"   SQL Query: {result.sql_query}")
        print(f"   Datasource: {result.datasource}")
        print(f"   Columns: {result.columns}")
        print(f"   Row Count: {result.row_count}")
        print(f"   Error: {result.error}")
        
        if result.success and result.data:
            print(f"\n📋 Sample Data (first 5 rows):")
            for i, row in enumerate(result.data[:5]):
                print(f"   {i+1}. {row}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_database())
