#!/usr/bin/env python3
"""
Test Excel Filtering Directly
"""

import asyncio
import pandas as pd
from src.report_manager.database.connection_manager import DatabaseConnectionManager, ExcelConnectionInfo
from src.report_manager.database.config_manager import DatabaseConfigManager
from src.report_manager.database.security import DatabaseSecurity
from datetime import datetime

async def test_excel_filtering_direct():
    """Test Excel filtering functionality directly"""
    
    print("🧪 Testing Excel Filtering Directly")
    print("=" * 50)
    
    try:
        # Initialize components
        config_manager = DatabaseConfigManager()
        security_manager = DatabaseSecurity(config_manager)
        connection_manager = DatabaseConnectionManager(config_manager, security_manager)
        
        # Load Excel file
        print("📊 Loading Excel file...")
        df = pd.read_excel("data/employees.xlsx", sheet_name="Employees")
        print(f"   Loaded {len(df)} employees with {len(df.columns)} columns")
        
        # Create mock Excel connection
        excel_connection = ExcelConnectionInfo(
            datasource_name="employee_db",
            file_path="data/employees.xlsx",
            sheet_name="Employees",
            dataframe=df,
            created_at=datetime.now(),
            last_used=datetime.now()
        )
        
        # Test WHERE clause parsing
        print("\n🔍 Testing WHERE clause parsing...")
        
        test_queries = [
            "SELECT * FROM Employees WHERE YearsOfService > 3",
            "SELECT * FROM Employees WHERE Department = 'HR'",
            "SELECT * FROM Employees WHERE Salary > 80000",
            "SELECT * FROM Employees WHERE YearsOfService < 2"
        ]
        
        for query in test_queries:
            print(f"\n📝 Query: {query}")
            
            # Mock user session
            class MockSession:
                def __init__(self):
                    self.user_id = "system"
                    self.role = "admin"
            
            session = MockSession()
            
            # Execute query
            result = await connection_manager._execute_excel_query(excel_connection, query, session)
            
            if result.success:
                print(f"   ✅ Success: {len(result.data)} rows returned")
                
                # Show sample results
                if result.data:
                    sample = result.data[0]
                    name = sample.get('FullName', 'N/A')
                    dept = sample.get('Department', 'N/A')
                    years = sample.get('YearsOfService', 'N/A')
                    salary = sample.get('Salary', 'N/A')
                    print(f"   📄 Sample: {name}, {dept}, {years} years, ${salary}")
                
                # Verify filtering worked
                if "YearsOfService > 3" in query:
                    filtered_count = len([r for r in result.data if r.get('YearsOfService', 0) > 3])
                    print(f"   🎯 Verification: {filtered_count}/{len(result.data)} have >3 years service")
                elif "Department = 'HR'" in query:
                    hr_count = len([r for r in result.data if r.get('Department') == 'HR'])
                    print(f"   🎯 Verification: {hr_count}/{len(result.data)} are in HR")
                elif "Salary > 80000" in query:
                    high_salary_count = len([r for r in result.data if r.get('Salary', 0) > 80000])
                    print(f"   🎯 Verification: {high_salary_count}/{len(result.data)} have salary >80K")
                elif "YearsOfService < 2" in query:
                    new_count = len([r for r in result.data if r.get('YearsOfService', 0) < 2])
                    print(f"   🎯 Verification: {new_count}/{len(result.data)} have <2 years service")
                    
            else:
                print(f"   ❌ Failed: {result.error}")
        
        print("\n" + "=" * 50)
        print("🎯 Direct Excel Filtering Test Complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_excel_filtering_direct())
