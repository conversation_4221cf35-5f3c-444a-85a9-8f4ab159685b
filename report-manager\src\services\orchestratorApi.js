import apiConfig from '../config/apiConfig.js';

// Orchestrator API Service
class OrchestratorApiService {
  constructor() {
    this.baseUrl = apiConfig.orchestrator.baseUrl;
    this.defaultHeaders = apiConfig.general.headers;
    this.timeout = apiConfig.orchestrator.timeout;
  }

  /**
   * Send a query to the orchestrator API
   * @param {string} text - The user's message/query
   * @param {boolean} complexFlow - Whether to use complex flow (default: false)
   * @param {string} outputFormat - Output format (default: "json")
   * @returns {Promise<Object>} API response
   */
  async sendQuery(text, complexFlow = false, outputFormat = "json") {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(`${this.baseUrl}${apiConfig.orchestrator.endpoints.query}`, {
        method: 'POST',
        headers: this.defaultHeaders,
        signal: controller.signal,
        body: JSON.stringify({
          text: text,
          complex_flow: complexFlow,
          output_format: outputFormat
        })
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('Request timeout - please try again');
      }
      console.error('Orchestrator API Error:', error);
      throw error;
    }
  }

  /**
   * Process a chat message and return formatted response
   * @param {string} userMessage - The user's message
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Formatted response object
   */
  async processChatMessage(userMessage, options = {}) {
    try {
      const {
        complexFlow = apiConfig.orchestrator.defaultOptions.complexFlow,
        outputFormat = apiConfig.orchestrator.defaultOptions.outputFormat
      } = options;

      const result = await this.sendQuery(userMessage, complexFlow, outputFormat);

      console.log('API Response:', result); // Debug log

      // Check if this is a structured response that should be rendered dynamically
      // Handle the nested structure: {"success":true,"result":{"success":true,"data":{...}}}
      const hasStructuredData = result.success && result.result && result.result.data;

      if (hasStructuredData) {
        // Return the full structured response for dynamic rendering
        return {
          success: true,
          message: result, // Pass the entire response object for dynamic rendering
          agents_used: result.agents_used || [],
          timestamp: new Date(),
          raw_response: result,
          isStructured: true
        };
      }

      // Handle simple responses
      let message = '';
      let agents_used = [];
      let success = false;

      if (result.success) {
        success = true;
        // Extract the actual response text from the data field
        if (typeof result.data === 'string') {
          message = result.data;
        } else if (result.data && typeof result.data === 'object') {
          // If data is an object, try to extract meaningful text
          message = result.data.response || result.data.answer || result.data.result || JSON.stringify(result.data);
        } else {
          message = 'Response received successfully.';
        }

        // Extract agents from metadata if available
        if (result.metadata && result.metadata.agents_used) {
          agents_used = result.metadata.agents_used;
        } else if (result.agents_used) {
          agents_used = result.agents_used;
        }
      } else {
        success = false;
        message = result.error || 'An error occurred while processing your request.';
      }

      // Format the response for chat interface
      return {
        success: success,
        message: message,
        agents_used: agents_used,
        timestamp: new Date(),
        raw_response: result,
        isStructured: false
      };
    } catch (error) {
      return {
        success: false,
        message: error.message.includes('timeout')
          ? 'Request timed out. Please try again with a shorter message.'
          : 'Sorry, I encountered an error processing your request. Please try again.',
        agents_used: [],
        timestamp: new Date(),
        error: error.message
      };
    }
  }

  /**
   * Check if the API is available
   * @returns {Promise<boolean>} True if API is available
   */
  async checkApiHealth() {
    try {
      const response = await fetch(`${this.baseUrl}${apiConfig.orchestrator.endpoints.health}`, {
        method: 'GET',
        headers: this.defaultHeaders
      });
      return response.ok;
    } catch (error) {
      console.error('API Health Check Failed:', error);
      return false;
    }
  }

  /**
   * Set custom base URL for the API
   * @param {string} url - New base URL
   */
  setBaseUrl(url) {
    this.baseUrl = url;
  }

  /**
   * Set custom headers
   * @param {Object} headers - Additional headers
   */
  setHeaders(headers) {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers };
  }
}

// Create and export a singleton instance
const orchestratorApi = new OrchestratorApiService();

export default orchestratorApi;

// Also export the class for custom instances if needed
export { OrchestratorApiService };
