import React, { useState } from 'react';
import streamingApi from '../../services/streamingApi';
import DownloadRenderer from '../DownloadRenderer/DownloadRenderer';
import './ReportGenerator.css';

const ReportGenerator = ({ userMessage, onReportGenerated }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedFormat, setSelectedFormat] = useState('excel');
  const [downloadInfo, setDownloadInfo] = useState(null);
  const [error, setError] = useState(null);

  const formats = [
    { value: 'excel', label: 'Excel (.xlsx)', icon: '📊' },
    { value: 'pdf', label: 'PDF (.pdf)', icon: '📄' },
    { value: 'csv', label: 'CSV (.csv)', icon: '📋' },
    { value: 'word', label: 'Word (.docx)', icon: '📝' }
  ];

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    setProgress(0);
    setError(null);
    setDownloadInfo(null);

    try {
      console.log('Starting report generation:', { userMessage, selectedFormat });

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 500);

      const result = await streamingApi.generateReport(
        userMessage,
        selectedFormat,
        (progressData) => {
          console.log('Progress update:', progressData);
          setProgress(progressData.percentage || 0);
        }
      );

      clearInterval(progressInterval);
      setProgress(100);

      console.log('Report generation result:', result);

      if (result.success && result.downloadInfo) {
        setDownloadInfo(result.downloadInfo);
        if (onReportGenerated) {
          onReportGenerated(result);
        }
      } else {
        setError(result.message || 'Failed to generate report');
        console.error('Report generation failed:', result);
      }
    } catch (error) {
      console.error('Report generation error:', error);
      setError(error.message || 'An error occurred while generating the report');
    } finally {
      setIsGenerating(false);
      setTimeout(() => setProgress(0), 1000); // Keep progress visible briefly
    }
  };

  const handleDownload = (downloadInfo) => {
    console.log('Report downloaded:', downloadInfo);
    // You can add analytics or other tracking here
  };

  const testApiConnection = async () => {
    try {
      console.log('Testing API connection...');
      const response = await fetch('http://localhost:8000/query/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'test query',
          output_format: 'excel'
        })
      });

      console.log('API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        url: response.url
      });

      if (response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json();
          console.log('JSON Response:', data);
        } else {
          console.log('Non-JSON response, likely a file');
          const blob = await response.blob();
          console.log('Blob info:', { size: blob.size, type: blob.type });
        }
      } else {
        console.error('API Error:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('API Test Failed:', error);
    }
  };

  if (downloadInfo) {
    return (
      <div className="report-generator">
        <DownloadRenderer 
          downloadInfo={downloadInfo} 
          onDownload={handleDownload}
        />
        <button 
          className="generate-another-btn"
          onClick={() => setDownloadInfo(null)}
        >
          🔄 Generate Another Report
        </button>
      </div>
    );
  }

  return (
    <div className="report-generator">
      <div className="generator-header">
        <span className="generator-title">📊 Generate Downloadable Report</span>
        <p className="generator-description">
          Create a downloadable report based on your query: "{userMessage}"
        </p>
      </div>

      <div className="format-selection">
        <label className="format-label">Choose Report Format:</label>
        <div className="format-options">
          {formats.map((format) => (
            <button
              key={format.value}
              className={`format-option ${selectedFormat === format.value ? 'selected' : ''}`}
              onClick={() => setSelectedFormat(format.value)}
              disabled={isGenerating}
            >
              <span className="format-icon">{format.icon}</span>
              <span className="format-text">{format.label}</span>
            </button>
          ))}
        </div>
      </div>

      {isGenerating && (
        <div className="progress-section">
          <div className="progress-header">
            <span className="progress-text">Generating report...</span>
            <span className="progress-percentage">{progress}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      )}

      {error && (
        <div className="error-section">
          <span className="error-icon">❌</span>
          <span className="error-text">{error}</span>
        </div>
      )}

      <div className="generator-actions">
        <button
          className="test-api-btn"
          onClick={testApiConnection}
          style={{ marginRight: '12px', padding: '8px 16px', fontSize: '12px' }}
        >
          🔧 Test API
        </button>

        <button
          className={`generate-btn ${isGenerating ? 'generating' : ''}`}
          onClick={handleGenerateReport}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <>
              <span className="spinner">⏳</span>
              Generating {selectedFormat.toUpperCase()}...
            </>
          ) : (
            <>
              🚀 Generate {selectedFormat.toUpperCase()} Report
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ReportGenerator;
