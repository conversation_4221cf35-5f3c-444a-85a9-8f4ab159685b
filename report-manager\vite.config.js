import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  define: {
    global: 'globalThis',
  },
  server: {
    port: 3001,
    host: true
  },
  build: {
    target: 'es2015',
    rollupOptions: {
      external: [],
    }
  },
  optimizeDeps: {
    include: ['react', 'react-dom'],
    exclude: ['vite']
  },
  resolve: {
    alias: {
      crypto: 'crypto-browserify'
    }
  }
})
