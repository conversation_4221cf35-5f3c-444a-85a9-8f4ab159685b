// API Configuration
const apiConfig = {
  // Orchestrator API settings
  orchestrator: {
    baseUrl: import.meta.env.VITE_ORCHESTRATOR_API_URL || 'http://localhost:8000',
    endpoints: {
      query: '/query',
      health: '/health'
    },
    defaultOptions: {
      complexFlow: false,
      outputFormat: 'json'
    },
    timeout: 30000 // 30 seconds
  },

  // General API settings
  general: {
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
    headers: {
      'Content-Type': 'application/json'
    }
  }
};

export default apiConfig;
