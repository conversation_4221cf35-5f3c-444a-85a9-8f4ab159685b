// Google OAuth Configuration
export const GOOGLE_AUTH_CONFIG = {
  // You'll need to replace this with your actual Google Client ID
  // Get it from: https://console.developers.google.com/
  clientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || 'demo-mode',

  // OAuth scopes - what permissions we're requesting
  scopes: [
    'openid',
    'email',
    'profile'
  ],

  // Redirect URI (should match what's configured in Google Console)
  redirectUri: import.meta.env.VITE_REDIRECT_URI || 'http://localhost:3001',

  // Additional OAuth settings
  responseType: 'token id_token',
  accessType: 'online',
  prompt: 'consent',
  includeGrantedScopes: true
};

// Demo mode configuration
export const DEMO_MODE = import.meta.env.VITE_DEMO_MODE === 'true' ||
                         GOOGLE_AUTH_CONFIG.clientId === 'demo-mode' ||
                         GOOGLE_AUTH_CONFIG.clientId === 'your-actual-client-id.apps.googleusercontent.com' ||
                         !GOOGLE_AUTH_CONFIG.clientId ||
                         GOOGLE_AUTH_CONFIG.clientId.includes('your-actual-client-id');

export const DEMO_USER = {
  sub: 'demo-user-123',
  name: 'Demo User',
  email: '<EMAIL>',
  picture: 'https://via.placeholder.com/96x96/3b82f6/ffffff?text=DU',
  given_name: 'Demo',
  family_name: 'User',
  exp: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now
};

// Auth endpoints
export const AUTH_ENDPOINTS = {
  googleAuth: 'https://accounts.google.com/o/oauth2/v2/auth',
  googleToken: 'https://oauth2.googleapis.com/token',
  googleUserInfo: 'https://www.googleapis.com/oauth2/v2/userinfo'
};

// Local storage keys
export const STORAGE_KEYS = {
  accessToken: 'auth_access_token',
  refreshToken: 'auth_refresh_token',
  userInfo: 'auth_user_info',
  expiresAt: 'auth_expires_at'
};
