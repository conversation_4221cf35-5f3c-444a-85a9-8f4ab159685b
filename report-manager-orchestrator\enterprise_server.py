#!/usr/bin/env python3
"""
🚀 ENTERPRISE INTELLIGENT SERVER - Seamless Natural Language to Data
"""

import os
import asyncio
import json
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv
from src.report_manager.database.query_engine import DatabaseQueryEngine

# Load environment variables FIRST
load_dotenv()

app = FastAPI(title="Enterprise Intelligent Report Manager", version="1.0.0")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global database query engine
db_query_engine = None

class QueryRequest(BaseModel):
    text: str
    output_format: str = "excel"
    save_to_file: bool = False
    filename: str = None
    complex_flow: bool = False

class StreamQueryRequest(BaseModel):
    text: str
    output_format: str = "excel"
    chunk_size: int = 50
    complex_flow: bool = False

@app.on_event("startup")
async def startup_event():
    """🚀 Initialize Enterprise Database on Startup"""
    global db_query_engine
    
    print("🚀 ENTERPRISE SERVER STARTING...")
    print("🔍 Environment Check:")
    print(f"   INVOICING_DB_TYPE: {os.getenv('INVOICING_DB_TYPE')}")
    print(f"   INVOICING_DB_PATH: {os.getenv('INVOICING_DB_PATH')}")
    print(f"   Database exists: {os.path.exists(os.getenv('INVOICING_DB_PATH', 'data/invoicing.db'))}")
    
    # Create a mock LLM client
    class MockLLMClient:
        async def generate_response(self, prompt, **kwargs):
            return "Mock response"
    
    try:
        # Initialize database query engine
        print("🔧 Initializing Enterprise Database Query Engine...")
        db_query_engine = DatabaseQueryEngine(MockLLMClient())
        
        # Initialize the engine
        print("🔧 Connecting to Enterprise Databases...")
        await db_query_engine.initialize()
        
        print("✅ Enterprise Database Query Engine Ready!")
        print("🧠 AI-powered intelligent query processing enabled")
        
    except Exception as e:
        print(f"❌ Error initializing enterprise database: {e}")
        import traceback
        traceback.print_exc()

@app.get("/health")
async def health():
    """Health check"""
    return {
        "status": "healthy",
        "enterprise_ready": db_query_engine is not None,
        "timestamp": datetime.now().isoformat(),
        "ai_engine": "database_query_engine" if db_query_engine else "not_available"
    }

@app.post("/query/excel")
async def enterprise_query(request: QueryRequest):
    """🚀 ENTERPRISE INTELLIGENT QUERY - Seamless Natural Language to Data"""
    try:
        print(f"🧠 ENTERPRISE QUERY: {request.text}")
        
        # Use intelligent database query engine
        if db_query_engine:
            try:
                # Create a mock session (in enterprise, this would be real user session)
                class MockSession:
                    def __init__(self):
                        self.user_id = "system"
                        self.role = "admin"

                session = MockSession()
                
                # 🧠 LET AI INTELLIGENTLY DETERMINE EVERYTHING
                print(f"🔍 AI ANALYZING: '{request.text}'")
                result = await db_query_engine.execute_natural_language_query(request.text, session)
                
                if result.success:
                    print(f"✅ AI-GENERATED SQL: {result.sql_query}")
                    print(f"🗄️ AUTO-SELECTED DB: {result.datasource}")
                    
                    # 🎯 INTELLIGENTLY DETERMINE SHEET NAME
                    sheet_name = "Data"
                    query_lower = request.text.lower()
                    if "tax" in query_lower and ("code" in query_lower or "einvoicing" in query_lower):
                        sheet_name = "Tax Codes"
                    elif "invoice" in query_lower:
                        sheet_name = "Invoices"
                    elif "customer" in query_lower:
                        sheet_name = "Customers"
                    elif "product" in query_lower:
                        sheet_name = "Products"
                    elif "sales" in query_lower:
                        sheet_name = "Sales Data"
                    elif "report" in query_lower:
                        sheet_name = "Report"
                    
                    # 📊 BUILD INTELLIGENT RESPONSE
                    response_data = {
                        "success": True,
                        "metadata": {
                            "title": f"Report: {request.text}",
                            "generated_at": datetime.now().isoformat(),
                            "query": request.text,
                            "sql_query": result.sql_query,
                            "datasource": result.datasource,
                            "confidence": getattr(result, 'confidence', 1.0),
                            "explanation": "AI-powered enterprise query executed successfully",
                            "query_type": "enterprise_intelligent_analysis",
                            "ai_engine": "database_query_engine"
                        },
                        "sheets": [{
                            "name": sheet_name,
                            "headers": result.columns,
                            "rows": [list(row.values()) for row in result.data] if result.data else [],
                            "total_rows": len(result.data) if result.data else 0
                        }],
                        "summary": {
                            "total_records": len(result.data) if result.data else 0,
                            "datasets_returned": 1,
                            "ai_confidence": getattr(result, 'confidence', 1.0),
                            "processing_time": getattr(result, 'execution_time', 0),
                            "datasource_used": result.datasource,
                            "query_complexity": "enterprise_intelligent_analysis"
                        }
                    }
                    
                    print(f"📤 ENTERPRISE RESPONSE: {len(result.data) if result.data else 0} rows")
                    print(f"🎯 SHEET NAME: {sheet_name}")
                    
                    return response_data
                    
                else:
                    # Query failed
                    return {
                        "success": False,
                        "error": f"Query execution failed: {result.error}",
                        "metadata": {
                            "title": f"Report: {request.text}",
                            "generated_at": datetime.now().isoformat(),
                            "query": request.text,
                            "sql_query": getattr(result, 'sql_query', 'N/A'),
                            "datasource": getattr(result, 'datasource', 'N/A'),
                            "explanation": "AI query execution failed",
                            "query_type": "enterprise_intelligent_analysis"
                        }
                    }
                    
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Enterprise query engine error: {str(e)}",
                    "metadata": {
                        "title": f"Report: {request.text}",
                        "generated_at": datetime.now().isoformat(),
                        "query": request.text,
                        "explanation": "Enterprise query engine encountered an error",
                        "query_type": "enterprise_intelligent_analysis"
                    }
                }

        # Database not available
        return {
            "success": False,
            "error": "Enterprise database query engine not available.",
            "metadata": {
                "title": f"Report: {request.text}",
                "generated_at": datetime.now().isoformat(),
                "query": request.text,
                "explanation": "Enterprise database query engine not initialized",
                "query_type": "enterprise_intelligent_analysis"
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/query/stream")
async def enterprise_stream_query(request: StreamQueryRequest):
    """🚀 ENTERPRISE INTELLIGENT STREAMING - Real-time AI-powered Data Streaming"""
    try:
        from fastapi.responses import StreamingResponse
        from typing import AsyncGenerator

        async def generate_stream() -> AsyncGenerator[str, None]:
            try:
                # Send initial metadata
                metadata = {
                    "type": "metadata",
                    "query": request.text,
                    "timestamp": datetime.now().isoformat(),
                    "format": request.output_format,
                    "ai_engine": "enterprise_intelligent_streaming"
                }
                yield f"data: {json.dumps(metadata)}\n\n"

                print(f"🧠 ENTERPRISE STREAM QUERY: {request.text}")

                # Use intelligent database query engine
                if db_query_engine:
                    try:
                        # Create a mock session
                        class MockSession:
                            def __init__(self):
                                self.user_id = "system"
                                self.role = "admin"

                        session = MockSession()

                        # 🧠 LET AI INTELLIGENTLY DETERMINE EVERYTHING
                        print(f"🔍 AI STREAM ANALYZING: '{request.text}'")
                        result = await db_query_engine.execute_natural_language_query(request.text, session)

                        # DEBUG: Check result status
                        print(f"🔍 STREAM RESULT DEBUG:")
                        print(f"   Success: {result.success}")
                        print(f"   Error: {result.error}")
                        print(f"   SQL: {getattr(result, 'sql_query', 'N/A')}")
                        print(f"   Data count: {len(result.data) if hasattr(result, 'data') and result.data else 0}")

                        if result.success:
                            print(f"✅ AI STREAM SQL: {result.sql_query}")
                            print(f"🗄️ AI STREAM DB: {result.datasource}")

                            # 🎯 INTELLIGENTLY DETERMINE SHEET NAME
                            sheet_name = "Data"
                            query_lower = request.text.lower()
                            if "tax" in query_lower and ("code" in query_lower or "einvoicing" in query_lower):
                                sheet_name = "Tax Codes"
                            elif "invoice" in query_lower:
                                sheet_name = "Invoices"
                            elif "customer" in query_lower:
                                sheet_name = "Customers"
                            elif "product" in query_lower:
                                sheet_name = "Products"
                            elif "sales" in query_lower:
                                sheet_name = "Sales Data"
                            elif "report" in query_lower:
                                sheet_name = "Report"

                            print(f"📤 ENTERPRISE STREAM: {len(result.data) if result.data else 0} rows")
                            print(f"🎯 STREAM SHEET: {sheet_name}")

                            # 📊 STREAM 1: Send Excel Metadata
                            excel_metadata = {
                                "type": "excel_metadata",
                                "title": f"Report: {request.text}",
                                "generated_at": datetime.now().isoformat(),
                                "query": request.text,
                                "sql_query": result.sql_query,
                                "datasource": result.datasource,
                                "confidence": getattr(result, 'confidence', 1.0),
                                "explanation": "AI-powered enterprise query executed successfully",
                                "ai_engine": "database_query_engine",
                                "total_sheets": 1,
                                "timestamp": datetime.now().isoformat()
                            }
                            yield f"data: {json.dumps(excel_metadata)}\n\n"

                            # 📊 STREAM 2: Send Sheet Info
                            sheet_info = {
                                "type": "sheet_info",
                                "sheet_index": 0,
                                "sheet_name": sheet_name,
                                "total_rows": len(result.data) if result.data else 0,
                                "total_columns": len(result.columns),
                                "headers": result.columns,
                                "timestamp": datetime.now().isoformat()
                            }
                            yield f"data: {json.dumps(sheet_info)}\n\n"

                            # 📊 STREAM 3: Send Data in Chunks
                            rows_data = [list(row.values()) for row in result.data] if result.data else []
                            chunk_size = request.chunk_size
                            total_chunks = (len(rows_data) + chunk_size - 1) // chunk_size if rows_data else 0

                            for chunk_index in range(total_chunks):
                                start_idx = chunk_index * chunk_size
                                end_idx = min(start_idx + chunk_size, len(rows_data))
                                chunk_rows = rows_data[start_idx:end_idx]

                                data_chunk = {
                                    "type": "sheet_data",
                                    "sheet_index": 0,
                                    "sheet_name": sheet_name,
                                    "chunk_index": chunk_index,
                                    "total_chunks": total_chunks,
                                    "rows": chunk_rows,
                                    "start_row": start_idx,
                                    "end_row": end_idx - 1,
                                    "chunk_size": len(chunk_rows),
                                    "timestamp": datetime.now().isoformat()
                                }
                                yield f"data: {json.dumps(data_chunk)}\n\n"

                            # 📊 STREAM 4: Send Simple Completion Signal
                            completion = {
                                "type": "complete",
                                "success": True,
                                "execution_time": getattr(result, 'execution_time', 0.1),
                                "ai_confidence": getattr(result, 'confidence', 1.0),
                                "datasource_used": result.datasource,
                                "total_rows": len(rows_data),
                                "message": "Streaming completed successfully"
                            }
                            yield f"data: {json.dumps(completion)}\n\n"
                            return
                        else:
                            # Query failed - send error
                            error_payload = {
                                "type": "error",
                                "error": f"Query execution failed: {result.error}",
                                "timestamp": datetime.now().isoformat(),
                                "query_type": "enterprise_intelligent_streaming"
                            }
                            yield f"data: {json.dumps(error_payload)}\n\n"
                            return

                    except Exception as e:
                        print(f"❌ Enterprise stream query failed: {e}")
                        error_payload = {
                            "type": "error",
                            "error": f"Enterprise query engine error: {str(e)}",
                            "timestamp": datetime.now().isoformat(),
                            "query_type": "enterprise_intelligent_streaming"
                        }
                        yield f"data: {json.dumps(error_payload)}\n\n"
                        return

                # Database not available
                error_payload = {
                    "type": "error",
                    "error": "Enterprise database query engine not available.",
                    "timestamp": datetime.now().isoformat(),
                    "query_type": "enterprise_intelligent_streaming"
                }
                yield f"data: {json.dumps(error_payload)}\n\n"

            except Exception as e:
                error_payload = {
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                    "query_type": "enterprise_intelligent_streaming"
                }
                yield f"data: {json.dumps(error_payload)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/query/excel-clean")
async def clean_excel_query(request: QueryRequest):
    """🎯 ULTRA-CLEAN EXCEL ENDPOINT - Exactly What Frontend Expects"""
    try:
        print(f"🎯 CLEAN EXCEL QUERY: {request.text}")

        if db_query_engine:
            try:
                # Create a mock session
                class MockSession:
                    def __init__(self):
                        self.user_id = "system"
                        self.role = "admin"

                session = MockSession()

                # Execute query
                result = await db_query_engine.execute_natural_language_query(request.text, session)

                if result.success:
                    print(f"✅ CLEAN SQL: {result.sql_query}")
                    print(f"📊 CLEAN DATA: {len(result.data)} rows")

                    # Determine sheet name
                    sheet_name = "Data"
                    query_lower = request.text.lower()
                    if "tax" in query_lower and ("code" in query_lower or "einvoicing" in query_lower):
                        sheet_name = "Tax Codes"
                    elif "invoice" in query_lower:
                        sheet_name = "Invoices"
                    elif "customer" in query_lower:
                        sheet_name = "Customers"
                    elif "product" in query_lower:
                        sheet_name = "Products"
                    elif "sales" in query_lower:
                        sheet_name = "Sales Data"

                    # Return EXACTLY what frontend expects - no nesting, no duplicates
                    return {
                        "success": True,
                        "sheets": [{
                            "name": sheet_name,
                            "headers": result.columns,
                            "rows": [list(row.values()) for row in result.data] if result.data else []
                        }],
                        "metadata": {
                            "title": f"Report: {request.text}",
                            "generated_at": datetime.now().isoformat(),
                            "query": request.text,
                            "sql_query": result.sql_query,
                            "datasource": result.datasource,
                            "confidence": getattr(result, 'confidence', 1.0)
                        }
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Query execution failed: {result.error}"
                    }

            except Exception as e:
                return {
                    "success": False,
                    "error": f"Query engine error: {str(e)}"
                }

        return {
            "success": False,
            "error": "Database query engine not available"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/query/excel-simple")
async def simple_excel_query(request: QueryRequest):
    """🚀 SIMPLE EXCEL ENDPOINT - Clean Excel Data Only"""
    try:
        print(f"🧠 SIMPLE EXCEL QUERY: {request.text}")

        if db_query_engine:
            try:
                # Create a mock session
                class MockSession:
                    def __init__(self):
                        self.user_id = "system"
                        self.role = "admin"

                session = MockSession()

                # Execute query
                result = await db_query_engine.execute_natural_language_query(request.text, session)

                if result.success:
                    print(f"✅ SIMPLE SQL: {result.sql_query}")
                    print(f"📊 SIMPLE DATA: {len(result.data)} rows")

                    # Determine sheet name
                    sheet_name = "Data"
                    query_lower = request.text.lower()
                    if "tax" in query_lower and ("code" in query_lower or "einvoicing" in query_lower):
                        sheet_name = "Tax Codes"
                    elif "invoice" in query_lower:
                        sheet_name = "Invoices"
                    elif "customer" in query_lower:
                        sheet_name = "Customers"
                    elif "product" in query_lower:
                        sheet_name = "Products"
                    elif "sales" in query_lower:
                        sheet_name = "Sales Data"

                    # Return clean Excel data
                    return {
                        "success": True,
                        "metadata": {
                            "title": f"Report: {request.text}",
                            "generated_at": datetime.now().isoformat(),
                            "query": request.text,
                            "sql_query": result.sql_query,
                            "datasource": result.datasource,
                            "confidence": getattr(result, 'confidence', 1.0),
                            "ai_engine": "database_query_engine"
                        },
                        "sheets": [{
                            "name": sheet_name,
                            "headers": result.columns,
                            "rows": [list(row.values()) for row in result.data] if result.data else [],
                            "total_rows": len(result.data) if result.data else 0
                        }]
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Query execution failed: {result.error}",
                        "metadata": {
                            "query": request.text,
                            "sql_query": getattr(result, 'sql_query', 'N/A'),
                            "datasource": getattr(result, 'datasource', 'N/A')
                        }
                    }

            except Exception as e:
                return {
                    "success": False,
                    "error": f"Query engine error: {str(e)}",
                    "metadata": {
                        "query": request.text
                    }
                }

        return {
            "success": False,
            "error": "Database query engine not available",
            "metadata": {
                "query": request.text
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Enterprise Intelligent Server on port 8009...")
    uvicorn.run(app, host="127.0.0.1", port=8009, log_level="info")
