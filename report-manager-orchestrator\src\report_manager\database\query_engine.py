"""
Database Query Execution Engine

This module provides the main interface for executing database queries,
combining text-to-SQL conversion, security validation, and result formatting.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from loguru import logger

from ..core.context import User<PERSON>ontext, TaskType
from ..core.result import Query<PERSON><PERSON>ult as CoreQueryResult
from .config_manager import DatabaseConfigManager
from .security import DatabaseSecurity, UserSession
from .connection_manager import DatabaseConnectionManager, QueryR<PERSON>ult as DBQueryResult
from ..agents.sql_agent import SQLAgent
from .schema_discovery import DynamicSchemaDiscovery


@dataclass
class QueryExecutionResult:
    """Result of query execution with enhanced metadata"""
    success: bool
    data: Optional[List[Dict[str, Any]]] = None
    sql_query: Optional[str] = None
    datasource: Optional[str] = None
    tables: Optional[List[str]] = None
    columns: Optional[List[str]] = None
    row_count: int = 0
    execution_time: float = 0.0
    sql_generation_time: float = 0.0
    total_time: float = 0.0
    error: Optional[str] = None
    warnings: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class DatabaseQueryEngine:
    """
    Main database query execution engine that orchestrates
    text-to-SQL conversion, security validation, and query execution.
    """
    
    def __init__(self, llm_client):
        """
        Initialize the query engine
        
        Args:
            llm_client: Language model client for AI-powered features
        """
        self.llm_client = llm_client
        
        # Initialize components
        self.config_manager = DatabaseConfigManager()
        self.security_manager = DatabaseSecurity(self.config_manager)
        self.connection_manager = DatabaseConnectionManager(self.config_manager, self.security_manager)
        self.schema_discovery = DynamicSchemaDiscovery(self.connection_manager, self.config_manager)
        self.sql_agent = SQLAgent(llm_client, self.config_manager, self.schema_discovery)
        
        # Performance tracking
        self.query_stats = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "avg_execution_time": 0.0
        }
        
        logger.info("Database query engine initialized")
    
    async def initialize(self):
        """Initialize the query engine and all its components"""
        try:
            # Initialize database connections
            await self.connection_manager.initialize_connections()
            
            # Test connections
            await self._test_all_connections()
            
            logger.info("Database query engine initialization completed")
            
        except Exception as e:
            logger.error(f"Error initializing database query engine: {e}")
            raise
    
    async def execute_natural_language_query(self, query_text: str, user_session: UserSession, 
                                           context: Optional[UserContext] = None) -> QueryExecutionResult:
        """
        Execute a natural language query by converting it to SQL and executing it
        
        Args:
            query_text: Natural language query
            user_session: User session for security validation
            context: Optional user context
            
        Returns:
            Query execution result
        """
        start_time = datetime.now()
        warnings = []
        
        try:
            logger.info(f"Executing natural language query: {query_text}")
            
            # Create context if not provided
            if not context:
                context = UserContext(
                    intent=query_text,
                    entities=[],
                    task_type=TaskType.QUERY,
                    confidence=1.0,
                    parameters={}
                )
            
            # Step 1: Convert natural language to SQL (SINGLE RESULT ONLY)
            sql_start_time = datetime.now()
            sql_result = await self.sql_agent.process(context)
            sql_generation_time = (datetime.now() - sql_start_time).total_seconds()

            if not sql_result.success:
                return QueryExecutionResult(
                    success=False,
                    error=f"SQL generation failed: {sql_result.error}",
                    sql_generation_time=sql_generation_time,
                    total_time=(datetime.now() - start_time).total_seconds()
                )

            # Ensure we have ONLY ONE result - no multiple datasets
            logger.info(f"Generated single SQL query with confidence: {sql_result.data.get('confidence', 0)}")
            
            # Extract SQL information
            sql_data = sql_result.data
            sql_query = sql_data.get("sql_query")
            datasource = sql_data.get("datasource")
            tables = sql_data.get("tables", [])
            
            if not sql_query or not datasource:
                return QueryExecutionResult(
                    success=False,
                    error="Invalid SQL generation result",
                    sql_generation_time=sql_generation_time,
                    total_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Step 2: Execute SQL query
            db_result = await self.connection_manager.execute_query(
                sql_query, datasource, user_session
            )
            
            # Update statistics
            self._update_query_stats(db_result.success, db_result.execution_time)
            
            # Prepare result
            total_time = (datetime.now() - start_time).total_seconds()
            
            if db_result.success:
                # Add warnings if needed
                if db_result.row_count == 0:
                    warnings.append("Query returned no results")
                elif db_result.row_count >= self.security_manager.security_settings.get('query_result_limit', 10000):
                    warnings.append("Results may be truncated due to row limit")
                
                return QueryExecutionResult(
                    success=True,
                    data=db_result.data,
                    sql_query=sql_query,
                    datasource=datasource,
                    tables=tables,
                    columns=db_result.columns,
                    row_count=db_result.row_count,
                    execution_time=db_result.execution_time,
                    sql_generation_time=sql_generation_time,
                    total_time=total_time,
                    warnings=warnings if warnings else None,
                    metadata={
                        "sql_confidence": sql_data.get("confidence", 0.0),
                        "sql_explanation": sql_data.get("explanation", ""),
                        "relevant_datasources": sql_result.metadata.get("relevant_datasources", []),
                        "relevant_tables": sql_result.metadata.get("relevant_tables", [])
                    }
                )
            else:
                return QueryExecutionResult(
                    success=False,
                    error=db_result.error,
                    sql_query=sql_query,
                    datasource=datasource,
                    tables=tables,
                    execution_time=db_result.execution_time,
                    sql_generation_time=sql_generation_time,
                    total_time=total_time
                )
                
        except Exception as e:
            total_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Error executing natural language query: {e}")
            
            self._update_query_stats(False, 0.0)
            
            return QueryExecutionResult(
                success=False,
                error=str(e),
                total_time=total_time
            )
    
    async def execute_sql_query(self, sql_query: str, datasource: str, 
                               user_session: UserSession) -> QueryExecutionResult:
        """
        Execute a direct SQL query
        
        Args:
            sql_query: SQL query to execute
            datasource: Target data source
            user_session: User session for security validation
            
        Returns:
            Query execution result
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Executing direct SQL query on {datasource}")
            
            # Execute query
            db_result = await self.connection_manager.execute_query(
                sql_query, datasource, user_session
            )
            
            # Update statistics
            self._update_query_stats(db_result.success, db_result.execution_time)
            
            total_time = (datetime.now() - start_time).total_seconds()
            
            return QueryExecutionResult(
                success=db_result.success,
                data=db_result.data,
                sql_query=sql_query,
                datasource=datasource,
                columns=db_result.columns,
                row_count=db_result.row_count,
                execution_time=db_result.execution_time,
                total_time=total_time,
                error=db_result.error
            )
            
        except Exception as e:
            total_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Error executing SQL query: {e}")
            
            self._update_query_stats(False, 0.0)
            
            return QueryExecutionResult(
                success=False,
                error=str(e),
                sql_query=sql_query,
                datasource=datasource,
                total_time=total_time
            )
    
    async def get_available_tables(self, user_session: UserSession, 
                                  datasource: Optional[str] = None) -> Dict[str, Any]:
        """
        Get available tables for a user
        
        Args:
            user_session: User session
            datasource: Optional specific data source
            
        Returns:
            Available tables information
        """
        try:
            available_tables = {}
            
            # Get data sources to check
            if datasource:
                data_sources = {datasource: self.config_manager.get_data_source(datasource)}
            else:
                data_sources = self.config_manager.get_all_data_sources()
            
            for ds_name, ds_config in data_sources.items():
                if not ds_config:
                    continue
                
                ds_tables = {}
                
                for table_name, table_info in ds_config.tables.items():
                    # Check user permissions
                    if self.config_manager.validate_user_permissions(
                        user_session.role, ds_name, table_name
                    ):
                        ds_tables[table_name] = {
                            "description": table_info.description,
                            "keywords": table_info.keywords,
                            "column_count": len(table_info.columns),
                            "common_queries": table_info.common_queries
                        }
                
                if ds_tables:
                    available_tables[ds_name] = {
                        "name": ds_config.name,
                        "description": ds_config.description,
                        "type": ds_config.type,
                        "tables": ds_tables
                    }
            
            return {
                "user": user_session.user_id,
                "role": user_session.role,
                "available_datasources": len(available_tables),
                "datasources": available_tables
            }
            
        except Exception as e:
            logger.error(f"Error getting available tables: {e}")
            return {"error": str(e)}
    
    async def get_table_schema(self, datasource: str, table: str, 
                              user_session: UserSession) -> Optional[Dict[str, Any]]:
        """
        Get detailed schema for a specific table
        
        Args:
            datasource: Data source name
            table: Table name
            user_session: User session
            
        Returns:
            Table schema information
        """
        return await self.connection_manager.get_table_schema(datasource, table, user_session)
    
    async def suggest_queries(self, query_text: str, user_session: UserSession) -> List[str]:
        """
        Suggest possible queries based on input text
        
        Args:
            query_text: Partial or complete query text
            user_session: User session
            
        Returns:
            List of suggested queries
        """
        try:
            suggestions = []
            query_lower = query_text.lower()
            
            # Get available data sources
            relevant_datasources = self.config_manager.find_relevant_datasources(query_text)
            
            for ds_name in relevant_datasources:
                datasource = self.config_manager.get_data_source(ds_name)
                if not datasource:
                    continue
                
                # Check table suggestions
                for table_name, table_info in datasource.tables.items():
                    # Check permissions
                    if not self.config_manager.validate_user_permissions(
                        user_session.role, ds_name, table_name
                    ):
                        continue
                    
                    # Add common queries for this table
                    for common_query in table_info.common_queries:
                        if any(keyword in query_lower for keyword in table_info.keywords):
                            suggestions.append(common_query)
            
            # Add template suggestions
            templates = self.config_manager.get_query_templates()
            for template_name, template_config in templates.items():
                template_keywords = template_name.replace('_', ' ').split()
                if any(keyword in query_lower for keyword in template_keywords):
                    description = template_config.get('description', template_name)
                    suggestions.append(description)
            
            # Remove duplicates and limit
            suggestions = list(set(suggestions))[:10]
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating query suggestions: {e}")
            return []
    
    async def _test_all_connections(self):
        """Test all database connections"""
        data_sources = self.config_manager.get_all_data_sources()
        
        for ds_name in data_sources.keys():
            success = await self.connection_manager.test_connection(ds_name)
            if success:
                logger.info(f"✅ Connection test passed for {ds_name}")
            else:
                logger.warning(f"❌ Connection test failed for {ds_name}")
    
    def _update_query_stats(self, success: bool, execution_time: float):
        """Update query execution statistics"""
        self.query_stats["total_queries"] += 1
        
        if success:
            self.query_stats["successful_queries"] += 1
        else:
            self.query_stats["failed_queries"] += 1
        
        # Update average execution time
        total_time = (self.query_stats["avg_execution_time"] * (self.query_stats["total_queries"] - 1) + execution_time)
        self.query_stats["avg_execution_time"] = total_time / self.query_stats["total_queries"]
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        Get query engine status and statistics
        
        Returns:
            Engine status information
        """
        return {
            "status": "running",
            "query_statistics": self.query_stats,
            "connection_status": self.connection_manager.get_connection_status(),
            "security_report": self.security_manager.get_security_report(),
            "available_datasources": len(self.config_manager.get_all_data_sources()),
            "total_tables": sum(
                len(ds.tables) for ds in self.config_manager.get_all_data_sources().values()
            )
        }
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            await self.connection_manager.close_connections()
            self.security_manager.cleanup_expired_sessions()
            logger.info("Database query engine cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
